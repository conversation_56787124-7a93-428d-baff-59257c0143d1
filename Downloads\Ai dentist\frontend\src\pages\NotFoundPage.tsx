/**
 * 404 Not Found Page component
 * صفحة غير موجود 404
 */

import React from 'react';
import {
  Box,
  Typography,
  Button,
  Container,
  Paper,
} from '@mui/material';
import { Home, ArrowBack } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useI18n } from '../i18n/useI18n';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();
  const { t, currentLanguage } = useI18n();

  return (
    <Container maxWidth="md" sx={{ mt: 8 }}>
      <Paper
        elevation={3}
        sx={{
          p: 6,
          textAlign: 'center',
          borderRadius: 3,
        }}
      >
        <Typography
          variant="h1"
          sx={{
            fontSize: '6rem',
            fontWeight: 'bold',
            color: 'primary.main',
            mb: 2,
          }}
        >
          404
        </Typography>
        
        <Typography variant="h4" gutterBottom color="text.primary">
          {currentLanguage === 'ar' 
            ? 'الصفحة غير موجودة'
            : currentLanguage === 'fr'
            ? 'Page non trouvée'
            : 'Page Not Found'
          }
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          {currentLanguage === 'ar' 
            ? 'عذراً، الصفحة التي تبحث عنها غير موجودة.'
            : currentLanguage === 'fr'
            ? 'Désolé, la page que vous recherchez n\'existe pas.'
            : 'Sorry, the page you are looking for does not exist.'
          }
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
          <Button
            variant="contained"
            startIcon={<Home />}
            onClick={() => navigate('/dashboard')}
            size="large"
          >
            {currentLanguage === 'ar' 
              ? 'العودة للرئيسية'
              : currentLanguage === 'fr'
              ? 'Retour à l\'accueil'
              : 'Go Home'
            }
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<ArrowBack />}
            onClick={() => navigate(-1)}
            size="large"
          >
            {currentLanguage === 'ar' 
              ? 'رجوع'
              : currentLanguage === 'fr'
              ? 'Retour'
              : 'Go Back'
            }
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default NotFoundPage;
