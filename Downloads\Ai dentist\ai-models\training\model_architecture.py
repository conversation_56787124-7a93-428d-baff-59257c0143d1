"""
Advanced dental AI model architecture for comprehensive pathology detection
بنية نموذج الذكاء الاصطناعي المتقدم للكشف الشامل عن الأمراض السنية
"""

import torch
import torch.nn as nn
import torchvision.models as models
from typing import Dict, List, Tuple, Optional
import torch.nn.functional as F
import numpy as np
from enum import Enum


class DentalPathologyType(Enum):
    """Comprehensive dental pathology classification"""
    # Dental Pathologies / الأمراض السنية
    CARIES_SUPERFICIAL = "caries_superficial"  # تسوس سطحي
    CARIES_DEEP = "caries_deep"  # تسوس عميق
    CARIES_RECURRENT = "caries_recurrent"  # تسوس متكرر

    # Periapical Infections / العدوى حول الذروية
    PERIAPICAL_ABSCESS = "periapical_abscess"  # خراج حول الذروة
    GRANULOMA = "granuloma"  # ورم حبيبي
    PERIAPICAL_CYST = "periapical_cyst"  # كيس حول الذروة

    # Dental Fractures / كسور الأسنان
    ROOT_FRACTURE = "root_fracture"  # كسر الجذر
    CROWN_FRACTURE = "crown_fracture"  # كسر التاج
    VERTICAL_ROOT_FRACTURE = "vertical_root_fracture"  # كسر جذر عمودي

    # Impacted/Included Teeth / الأسنان المدفونة/المنطمرة
    IMPACTED_WISDOM = "impacted_wisdom"  # ضرس العقل المنطمر
    IMPACTED_CANINE = "impacted_canine"  # الناب المنطمر
    SUPERNUMERARY_TOOTH = "supernumerary_tooth"  # سن زائد

    # Dental Anomalies / شذوذات الأسنان
    AGENESIS = "agenesis"  # غياب خلقي للأسنان
    CONICAL_TOOTH = "conical_tooth"  # سن مخروطي
    DILACERATION = "dilaceration"  # انحناء الجذر

    # Root Resorption / امتصاص الجذر
    INTERNAL_RESORPTION = "internal_resorption"  # امتصاص داخلي
    EXTERNAL_RESORPTION = "external_resorption"  # امتصاص خارجي

    # Defective Restorations / الترميمات المعيبة
    OVERHANG_RESTORATION = "overhang_restoration"  # ترميم متدلي
    INFILTRATED_RESTORATION = "infiltrated_restoration"  # ترميم متسرب
    FRACTURED_RESTORATION = "fractured_restoration"  # ترميم مكسور

    # Foreign Bodies / الأجسام الغريبة
    FOREIGN_BODY = "foreign_body"  # جسم غريب
    MALPOSITIONED_IMPLANT = "malpositioned_implant"  # زرعة سيئة الوضع

    # Bone and Periodontal Pathologies / أمراض العظم واللثة
    HORIZONTAL_BONE_LOSS = "horizontal_bone_loss"  # فقدان عظم أفقي
    VERTICAL_BONE_LOSS = "vertical_bone_loss"  # فقدان عظم عمودي
    FURCATION_INVOLVEMENT = "furcation_involvement"  # إصابة منطقة التشعب

    # Cysts and Tumors / الأكياس والأورام
    DENTIGEROUS_CYST = "dentigerous_cyst"  # كيس جريبي
    RADICULAR_CYST = "radicular_cyst"  # كيس جذري
    ODONTOGENIC_TUMOR = "odontogenic_tumor"  # ورم سني المنشأ

    # TMJ Pathologies / أمراض المفصل الفكي الصدغي
    CONDYLAR_RESORPTION = "condylar_resorption"  # امتصاص اللقمة
    TMJ_ARTHRITIS = "tmj_arthritis"  # التهاب المفصل الفكي

    # Iatrogenic Elements / العناصر الطبية المنشأ
    DEFECTIVE_CROWN = "defective_crown"  # تاج معيب
    DEFECTIVE_BRIDGE = "defective_bridge"  # جسر معيب
    OVERFILLED_CANAL = "overfilled_canal"  # قناة مفرطة الحشو
    UNDERFILLED_CANAL = "underfilled_canal"  # قناة ناقصة الحشو
    FRACTURED_INSTRUMENT = "fractured_instrument"  # أداة مكسورة في القناة

    # Normal / طبيعي
    NORMAL = "normal"  # طبيعي


class MultiTaskDentalNet(nn.Module):
    """
    Advanced multi-task neural network for comprehensive dental pathology detection
    شبكة عصبية متعددة المهام للكشف الشامل عن الأمراض السنية
    """

    def __init__(self, num_pathologies: int = len(DentalPathologyType)):
        super(MultiTaskDentalNet, self).__init__()

        # Backbone: EfficientNet-B4 for better performance
        self.backbone = models.efficientnet_b4(pretrained=True)
        backbone_features = self.backbone.classifier.in_features
        self.backbone.classifier = nn.Identity()

        # Multi-scale feature extraction
        self.feature_pyramid = FeaturePyramidNetwork(backbone_features)

        # Attention mechanisms
        self.spatial_attention = SpatialAttentionModule(backbone_features)
        self.channel_attention = ChannelAttentionModule(backbone_features)

        # Task-specific heads
        self.pathology_classifier = PathologyClassifier(backbone_features, num_pathologies)
        self.severity_regressor = SeverityRegressor(backbone_features)
        self.localization_head = LocalizationHead(backbone_features)

        # Confidence estimation
        self.confidence_estimator = ConfidenceEstimator(backbone_features)

    def forward(self, x):
        # Extract backbone features
        features = self.backbone(x)

        # Apply attention mechanisms
        spatial_attended = self.spatial_attention(features)
        channel_attended = self.channel_attention(spatial_attended)

        # Multi-scale features
        pyramid_features = self.feature_pyramid(channel_attended)

        # Task predictions
        pathology_logits = self.pathology_classifier(pyramid_features)
        severity_scores = self.severity_regressor(pyramid_features)
        localization_maps = self.localization_head(pyramid_features)
        confidence_scores = self.confidence_estimator(pyramid_features)

        return {
            'pathology_logits': pathology_logits,
            'severity_scores': severity_scores,
            'localization_maps': localization_maps,
            'confidence_scores': confidence_scores,
            'attention_maps': spatial_attended
        }


class FeaturePyramidNetwork(nn.Module):
    """Feature Pyramid Network for multi-scale feature extraction"""

    def __init__(self, in_channels: int):
        super().__init__()
        self.lateral_convs = nn.ModuleList([
            nn.Conv2d(in_channels, 256, 1) for _ in range(4)
        ])
        self.fpn_convs = nn.ModuleList([
            nn.Conv2d(256, 256, 3, padding=1) for _ in range(4)
        ])

    def forward(self, x):
        # Implement FPN logic
        features = []
        current = x

        for lateral_conv, fpn_conv in zip(self.lateral_convs, self.fpn_convs):
            lateral = lateral_conv(current)
            features.append(fpn_conv(lateral))
            current = F.max_pool2d(current, 2)

        return torch.cat(features, dim=1)


class SpatialAttentionModule(nn.Module):
    """Spatial attention for focusing on relevant image regions"""

    def __init__(self, in_channels: int):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 8, 1),
            nn.ReLU(),
            nn.Conv2d(in_channels // 8, 1, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        attention = self.conv(x)
        return x * attention


class ChannelAttentionModule(nn.Module):
    """Channel attention for feature selection"""

    def __init__(self, in_channels: int):
        super().__init__()
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // 16),
            nn.ReLU(),
            nn.Linear(in_channels // 16, in_channels),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        attention = self.global_pool(x).view(b, c)
        attention = self.fc(attention).view(b, c, 1, 1)
        return x * attention


class PathologyClassifier(nn.Module):
    """Multi-label pathology classification head"""

    def __init__(self, in_features: int, num_pathologies: int):
        super().__init__()
        self.classifier = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Dropout(0.5),
            nn.Linear(in_features, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, num_pathologies)
        )

    def forward(self, x):
        return self.classifier(x)


class SeverityRegressor(nn.Module):
    """Severity scoring for detected pathologies"""

    def __init__(self, in_features: int):
        super().__init__()
        self.regressor = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(in_features, 256),
            nn.ReLU(),
            nn.Linear(256, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Sigmoid()  # Severity score between 0 and 1
        )

    def forward(self, x):
        return self.regressor(x)


class LocalizationHead(nn.Module):
    """Localization head for generating heatmaps"""

    def __init__(self, in_features: int):
        super().__init__()
        self.localization = nn.Sequential(
            nn.Conv2d(in_features, 256, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(256, 128, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(128, 1, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        return self.localization(x)


class ConfidenceEstimator(nn.Module):
    """Confidence estimation for AI predictions"""

    def __init__(self, in_features: int):
        super().__init__()
        self.confidence = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(in_features, 128),
            nn.ReLU(),
            nn.Linear(128, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        return self.confidence(x)


class GradCAMGenerator:
    """
    Grad-CAM visualization for model interpretability
    مولد Grad-CAM للتصور وقابلية تفسير النموذج
    """

    def __init__(self, model: nn.Module, target_layer: str):
        self.model = model
        self.target_layer = target_layer
        self.gradients = None
        self.activations = None

        # Register hooks
        self._register_hooks()

    def _register_hooks(self):
        """Register forward and backward hooks"""
        def forward_hook(module, input, output):
            self.activations = output

        def backward_hook(module, grad_input, grad_output):
            self.gradients = grad_output[0]

        # Find target layer and register hooks
        for name, module in self.model.named_modules():
            if name == self.target_layer:
                module.register_forward_hook(forward_hook)
                module.register_backward_hook(backward_hook)
                break

    def generate_cam(self, input_tensor: torch.Tensor, class_idx: int = None):
        """Generate Class Activation Map"""
        self.model.eval()

        # Forward pass
        output = self.model(input_tensor)

        if class_idx is None:
            class_idx = output.argmax(dim=1)

        # Backward pass
        self.model.zero_grad()
        class_score = output[:, class_idx].sum()
        class_score.backward()

        # Generate CAM
        gradients = self.gradients
        activations = self.activations

        # Global average pooling of gradients
        weights = torch.mean(gradients, dim=[2, 3], keepdim=True)

        # Weighted combination of activation maps
        cam = torch.sum(weights * activations, dim=1, keepdim=True)
        cam = F.relu(cam)

        # Normalize CAM
        cam = F.interpolate(cam, size=input_tensor.shape[2:], mode='bilinear', align_corners=False)
        cam = (cam - cam.min()) / (cam.max() - cam.min())

        return cam.squeeze().cpu().numpy()


class DentalPathologyDetector:
    """
    Complete dental pathology detection system
    نظام الكشف الكامل عن الأمراض السنية
    """

    def __init__(self, model_path: Optional[str] = None):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = MultiTaskDentalNet()
        self.model.to(self.device)

        if model_path:
            self.load_model(model_path)

        self.pathology_names = [pathology.value for pathology in DentalPathologyType]
        self.grad_cam = GradCAMGenerator(self.model, 'backbone.features')

    def load_model(self, model_path: str):
        """Load trained model weights"""
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()

    def preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """Preprocess dental X-ray image"""
        # Normalize to [0, 1]
        image = image.astype(np.float32) / 255.0

        # Resize to model input size
        image = torch.from_numpy(image).unsqueeze(0).unsqueeze(0)
        image = F.interpolate(image, size=(512, 512), mode='bilinear', align_corners=False)

        # Normalize with ImageNet stats (adapt for dental X-rays)
        mean = torch.tensor([0.485])
        std = torch.tensor([0.229])
        image = (image - mean.view(1, 1, 1, 1)) / std.view(1, 1, 1, 1)

        return image.to(self.device)

    def detect_pathologies(self, image: np.ndarray) -> Dict:
        """
        Comprehensive pathology detection
        الكشف الشامل عن الأمراض
        """
        # Preprocess image
        input_tensor = self.preprocess_image(image)

        with torch.no_grad():
            # Model inference
            outputs = self.model(input_tensor)

            # Process outputs
            pathology_probs = torch.sigmoid(outputs['pathology_logits']).cpu().numpy()[0]
            severity_score = outputs['severity_scores'].cpu().numpy()[0][0]
            confidence_score = outputs['confidence_scores'].cpu().numpy()[0][0]
            localization_map = outputs['localization_maps'].cpu().numpy()[0, 0]

        # Generate Grad-CAM for top prediction
        top_pathology_idx = np.argmax(pathology_probs)
        grad_cam_map = self.grad_cam.generate_cam(input_tensor, top_pathology_idx)

        # Prepare results
        detected_pathologies = []
        for i, prob in enumerate(pathology_probs):
            if prob > 0.5:  # Threshold for positive detection
                detected_pathologies.append({
                    'pathology': self.pathology_names[i],
                    'probability': float(prob),
                    'severity': self._get_severity_level(severity_score),
                    'confidence': float(confidence_score)
                })

        # Sort by probability
        detected_pathologies.sort(key=lambda x: x['probability'], reverse=True)

        return {
            'detected_pathologies': detected_pathologies,
            'overall_severity': float(severity_score),
            'confidence_score': float(confidence_score),
            'localization_heatmap': localization_map,
            'grad_cam_heatmap': grad_cam_map,
            'requires_urgent_attention': severity_score > 0.8 or any(
                p['pathology'] in ['periapical_abscess', 'vertical_root_fracture', 'odontogenic_tumor']
                for p in detected_pathologies
            )
        }

    def _get_severity_level(self, severity_score: float) -> str:
        """Convert severity score to categorical level"""
        if severity_score < 0.25:
            return 'low'
        elif severity_score < 0.5:
            return 'mild'
        elif severity_score < 0.75:
            return 'moderate'
        else:
            return 'severe'

    def compare_temporal_images(self, current_image: np.ndarray, previous_image: np.ndarray) -> Dict:
        """
        Compare current and previous X-rays for progression analysis
        مقارنة الأشعة الحالية والسابقة لتحليل التطور
        """
        current_results = self.detect_pathologies(current_image)
        previous_results = self.detect_pathologies(previous_image)

        # Analyze progression
        progression_analysis = {
            'new_pathologies': [],
            'resolved_pathologies': [],
            'progressed_pathologies': [],
            'stable_pathologies': []
        }

        current_pathologies = {p['pathology']: p for p in current_results['detected_pathologies']}
        previous_pathologies = {p['pathology']: p for p in previous_results['detected_pathologies']}

        # Find new pathologies
        for pathology in current_pathologies:
            if pathology not in previous_pathologies:
                progression_analysis['new_pathologies'].append(current_pathologies[pathology])

        # Find resolved pathologies
        for pathology in previous_pathologies:
            if pathology not in current_pathologies:
                progression_analysis['resolved_pathologies'].append(previous_pathologies[pathology])

        # Find progressed/stable pathologies
        for pathology in current_pathologies:
            if pathology in previous_pathologies:
                current_severity = current_pathologies[pathology]['probability']
                previous_severity = previous_pathologies[pathology]['probability']

                if current_severity > previous_severity + 0.1:
                    progression_analysis['progressed_pathologies'].append({
                        'pathology': pathology,
                        'previous_severity': previous_severity,
                        'current_severity': current_severity,
                        'progression': current_severity - previous_severity
                    })
                else:
                    progression_analysis['stable_pathologies'].append(current_pathologies[pathology])

        return {
            'current_analysis': current_results,
            'previous_analysis': previous_results,
            'progression_analysis': progression_analysis,
            'overall_progression': 'improved' if len(progression_analysis['resolved_pathologies']) > len(progression_analysis['new_pathologies']) else 'worsened' if len(progression_analysis['new_pathologies']) > 0 or len(progression_analysis['progressed_pathologies']) > 0 else 'stable'
        }

    def _generate_cam(self, features: torch.Tensor, predictions: torch.Tensor) -> torch.Tensor:
        """
        Generate Class Activation Maps (CAM) for visualization
        إنتاج خرائط تفعيل الفئات للتصور
        """
        # Get weights from the final linear layer
        weights = self.classifier[-1].weight  # Shape: (num_classes, 512)

        # We need to get the weights that correspond to the features
        # Since we have a more complex classifier, we'll use Grad-CAM approach
        batch_size, channels, height, width = features.shape

        # For simplicity, we'll create a basic CAM
        # In practice, you'd want to implement proper Grad-CAM
        cams = torch.zeros(batch_size, height, width)

        for i in range(batch_size):
            # Get the predicted class
            pred_class = torch.argmax(predictions[i])

            # Simple averaging of feature maps (placeholder for proper CAM)
            cam = torch.mean(features[i], dim=0)
            cams[i] = cam

        return cams


class DentalAttentionNet(nn.Module):
    """
    Attention-based network for dental X-ray analysis
    شبكة قائمة على الانتباه لتحليل الأشعة السينية للأسنان
    """

    def __init__(self, num_classes: int = 15):
        super(DentalAttentionNet, self).__init__()

        # Backbone CNN
        self.backbone = models.efficientnet_b0(pretrained=True)
        backbone_features = self.backbone.classifier.in_features
        self.backbone.classifier = nn.Identity()

        # Attention mechanism
        self.attention = SpatialAttention(backbone_features)

        # Classification head
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(backbone_features, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Extract features
        features = self.backbone.features(x)

        # Apply attention
        attended_features = self.attention(features)

        # Global pooling
        pooled = F.adaptive_avg_pool2d(attended_features, (1, 1))
        flattened = torch.flatten(pooled, 1)

        # Classification
        output = self.classifier(flattened)

        return output


class SpatialAttention(nn.Module):
    """
    Spatial attention mechanism for focusing on important regions
    آلية الانتباه المكاني للتركيز على المناطق المهمة
    """

    def __init__(self, in_channels: int):
        super(SpatialAttention, self).__init__()

        self.conv1 = nn.Conv2d(in_channels, in_channels // 8, 1)
        self.conv2 = nn.Conv2d(in_channels // 8, 1, 1)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Generate attention map
        attention = self.conv1(x)
        attention = F.relu(attention)
        attention = self.conv2(attention)
        attention = self.sigmoid(attention)

        # Apply attention to input features
        attended = x * attention

        return attended


class MultiTaskDentalNet(nn.Module):
    """
    Multi-task network for simultaneous pathology detection and severity assessment
    شبكة متعددة المهام للكشف المتزامن عن الأمراض وتقييم الخطورة
    """

    def __init__(self, num_pathologies: int = 15, num_severity_levels: int = 4):
        super(MultiTaskDentalNet, self).__init__()

        # Shared backbone
        self.backbone = models.resnet50(pretrained=True)
        feature_dim = self.backbone.fc.in_features
        self.backbone.fc = nn.Identity()

        # Shared feature processing
        self.shared_features = nn.Sequential(
            nn.Linear(feature_dim, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3)
        )

        # Pathology detection head
        self.pathology_head = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.2),
            nn.Linear(256, num_pathologies)
        )

        # Severity assessment head
        self.severity_head = nn.Sequential(
            nn.Linear(512, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(0.2),
            nn.Linear(128, num_severity_levels)
        )

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        # Extract features
        features = self.backbone(x)
        shared = self.shared_features(features)

        # Task-specific predictions
        pathology_pred = self.pathology_head(shared)
        severity_pred = self.severity_head(shared)

        return pathology_pred, severity_pred


class FocalLoss(nn.Module):
    """
    Focal Loss for handling class imbalance in dental pathology detection
    خسارة التركيز للتعامل مع عدم توازن الفئات في اكتشاف أمراض الأسنان
    """

    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


def create_model(model_type: str = "resnet", num_classes: int = 15, **kwargs) -> nn.Module:
    """
    Factory function to create different model architectures
    دالة المصنع لإنشاء بنى نماذج مختلفة
    """
    if model_type == "resnet":
        return DentalResNet(num_classes=num_classes, **kwargs)
    elif model_type == "attention":
        return DentalAttentionNet(num_classes=num_classes, **kwargs)
    elif model_type == "multitask":
        return MultiTaskDentalNet(num_pathologies=num_classes, **kwargs)
    else:
        raise ValueError(f"Unknown model type: {model_type}")


def count_parameters(model: nn.Module) -> int:
    """Count the number of trainable parameters in a model"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


if __name__ == "__main__":
    # Test model creation
    model = create_model("resnet", num_classes=15)
    print(f"Model created with {count_parameters(model):,} parameters")

    # Test forward pass
    dummy_input = torch.randn(1, 3, 224, 224)
    output = model(dummy_input)
    print(f"Output shape: {output.shape}")

    # Test with CAM
    output, cam = model(dummy_input, return_cam=True)
    print(f"Output shape: {output.shape}, CAM shape: {cam.shape}")
