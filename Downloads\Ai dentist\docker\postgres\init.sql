-- PostgreSQL initialization script for AI Dental Assistant
-- سكريبت تهيئة PostgreSQL لمساعد طبيب الأسنان الذكي

-- Create database if not exists
SELECT 'CREATE DATABASE dental_ai_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'dental_ai_db')\gexec

-- Connect to the database
\c dental_ai_db;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Create custom types
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('admin', 'dentist', 'assistant');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE analysis_status AS ENUM ('pending', 'processing', 'completed', 'failed', 'reviewed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE consultation_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance
-- These will be created by Alembic migrations, but we prepare the database

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE dental_ai_db TO dental_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO dental_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO dental_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO dental_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO dental_user;

-- Create audit function for INPDP compliance
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (
            table_name,
            operation,
            old_values,
            new_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            NULL,
            row_to_json(NEW),
            COALESCE(current_setting('app.current_user_id', true)::integer, 0),
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (
            table_name,
            operation,
            old_values,
            new_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            row_to_json(NEW),
            COALESCE(current_setting('app.current_user_id', true)::integer, 0),
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (
            table_name,
            operation,
            old_values,
            new_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            NULL,
            COALESCE(current_setting('app.current_user_id', true)::integer, 0),
            NOW()
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create function to set current user for audit
CREATE OR REPLACE FUNCTION set_current_user_id(user_id integer)
RETURNS void AS $$
BEGIN
    PERFORM set_config('app.current_user_id', user_id::text, false);
END;
$$ LANGUAGE plpgsql;

-- Create function for full-text search
CREATE OR REPLACE FUNCTION create_search_vector(
    first_name text,
    last_name text,
    first_name_ar text DEFAULT '',
    last_name_ar text DEFAULT '',
    national_id text DEFAULT ''
)
RETURNS tsvector AS $$
BEGIN
    RETURN to_tsvector('arabic', 
        COALESCE(first_name, '') || ' ' ||
        COALESCE(last_name, '') || ' ' ||
        COALESCE(first_name_ar, '') || ' ' ||
        COALESCE(last_name_ar, '') || ' ' ||
        COALESCE(national_id, '')
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function for Tunisian ID validation
CREATE OR REPLACE FUNCTION validate_tunisian_id(id_number text)
RETURNS boolean AS $$
BEGIN
    -- Basic validation for Tunisian national ID (8 digits)
    RETURN id_number ~ '^[0-9]{8}$';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function for CNAM number validation
CREATE OR REPLACE FUNCTION validate_cnam_number(cnam_number text)
RETURNS boolean AS $$
BEGIN
    -- Basic validation for CNAM number format
    RETURN cnam_number ~ '^[0-9]{9,12}$';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function to generate secure filenames
CREATE OR REPLACE FUNCTION generate_secure_filename(original_filename text)
RETURNS text AS $$
DECLARE
    extension text;
    secure_name text;
BEGIN
    -- Extract file extension
    extension := substring(original_filename from '\.([^.]*)$');
    
    -- Generate UUID-based filename
    secure_name := uuid_generate_v4()::text;
    
    -- Add extension if exists
    IF extension IS NOT NULL THEN
        secure_name := secure_name || '.' || extension;
    END IF;
    
    RETURN secure_name;
END;
$$ LANGUAGE plpgsql;

-- Create function for data anonymization (INPDP compliance)
CREATE OR REPLACE FUNCTION anonymize_patient_data(patient_id integer)
RETURNS void AS $$
BEGIN
    UPDATE patients SET
        first_name = 'ANONYMIZED',
        last_name = 'ANONYMIZED',
        first_name_ar = 'مجهول',
        last_name_ar = 'مجهول',
        email = '<EMAIL>',
        phone = '00000000',
        address = 'ANONYMIZED',
        national_id = '00000000',
        cnam_number = NULL,
        date_of_birth = '1900-01-01'
    WHERE id = patient_id;
    
    -- Log the anonymization
    INSERT INTO audit_logs (
        table_name,
        operation,
        old_values,
        new_values,
        user_id,
        timestamp,
        description
    ) VALUES (
        'patients',
        'ANONYMIZE',
        NULL,
        json_build_object('patient_id', patient_id),
        0,
        NOW(),
        'Patient data anonymized for INPDP compliance'
    );
END;
$$ LANGUAGE plpgsql;

-- Create function for data retention cleanup
CREATE OR REPLACE FUNCTION cleanup_old_data(retention_days integer DEFAULT 2555) -- 7 years default
RETURNS integer AS $$
DECLARE
    deleted_count integer := 0;
    cutoff_date timestamp;
BEGIN
    cutoff_date := NOW() - (retention_days || ' days')::interval;
    
    -- Delete old audit logs (keep for legal requirements)
    DELETE FROM audit_logs 
    WHERE timestamp < cutoff_date 
    AND table_name NOT IN ('patients', 'xray_analyses');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for performance (will be managed by Alembic)
-- These are just preparations

-- Performance optimization settings
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET pg_stat_statements.track = 'all';

-- Reload configuration
SELECT pg_reload_conf();

-- Create initial admin user (will be handled by application)
-- This is just a placeholder for the structure

COMMENT ON DATABASE dental_ai_db IS 'AI Dental Assistant Database - قاعدة بيانات مساعد طبيب الأسنان الذكي';

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO dental_user;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'AI Dental Assistant database initialized successfully!';
    RAISE NOTICE 'قاعدة بيانات مساعد طبيب الأسنان الذكي تم تهيئتها بنجاح!';
END $$;
