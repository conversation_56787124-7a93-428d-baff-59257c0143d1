@echo off
echo 🤖 AI Dental Assistant - Intelligent Data Organization Agent
echo ============================================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Check if sets folder exists
if not exist "sets" (
    echo ❌ 'sets' folder not found!
    echo.
    echo 📁 Please create a 'sets' folder and organize your dental X-ray images like this:
    echo.
    echo sets/
    echo ├── any_folder_name/
    echo │   ├── caries_case1.jpg
    echo │   ├── normal_tooth.png
    echo │   └── infection_xray.dcm
    echo ├── another_folder/
    echo │   ├── fracture_tooth.jpg
    echo │   └── healthy_teeth/
    echo │       ├── normal1.jpg
    echo │       └── normal2.jpg
    echo └── mixed_cases/
    echo     ├── tumor_case.png
    echo     └── cyst_patient.jpg
    echo.
    echo The agent will intelligently explore ALL folders and organize them automatically!
    echo.
    pause
    exit /b 1
)

echo ✅ Found 'sets' folder
echo 🤖 Launching Intelligent Data Organization Agent...
echo.

REM Install required dependencies if not present
echo 📦 Checking dependencies...
pip install opencv-python numpy >nul 2>&1

REM Run the intelligent agent
python scripts\intelligent_data_agent.py

echo.
echo ✅ Intelligent organization completed!
echo 📁 Check the 'ai-models\data' folder for organized data
echo 📄 Review the 'intelligence_report.json' for detailed analysis
echo.
pause
