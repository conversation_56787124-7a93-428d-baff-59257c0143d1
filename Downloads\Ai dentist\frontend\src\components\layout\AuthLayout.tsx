/**
 * Authentication Layout component
 * مكون تخطيط المصادقة
 */

import React from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  IconButton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { Language } from '@mui/icons-material';
import { useI18n } from '../../i18n/useI18n';

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t, changeLanguage, currentLanguage } = useI18n();

  const handleLanguageToggle = () => {
    const nextLanguage = currentLanguage === 'ar' ? 'fr' : currentLanguage === 'fr' ? 'en' : 'ar';
    changeLanguage(nextLanguage);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
      }}
    >
      {/* Language Toggle */}
      <IconButton
        onClick={handleLanguageToggle}
        sx={{
          position: 'absolute',
          top: 16,
          right: 16,
          color: 'white',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
          },
        }}
      >
        <Language />
      </IconButton>

      <Container component="main" maxWidth="sm">
        <Paper
          elevation={24}
          sx={{
            padding: isMobile ? 3 : 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            borderRadius: 3,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
          }}
        >
          {/* Logo and Title */}
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography
              variant="h3"
              component="h1"
              sx={{
                fontSize: isMobile ? '2rem' : '2.5rem',
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #1976d2, #dc004e)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1,
              }}
            >
              🦷 AI Dental
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                fontSize: isMobile ? '1rem' : '1.25rem',
                fontWeight: 500,
              }}
            >
              {currentLanguage === 'ar' 
                ? 'مساعد طبيب الأسنان الذكي'
                : currentLanguage === 'fr'
                ? 'Assistant Dentaire IA'
                : 'AI Dental Assistant'
              }
            </Typography>
          </Box>

          {/* Auth Form */}
          {children}

          {/* Footer */}
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              {currentLanguage === 'ar' 
                ? 'مصمم خصيصاً للسوق التونسي'
                : currentLanguage === 'fr'
                ? 'Conçu spécialement pour le marché tunisien'
                : 'Designed specifically for the Tunisian market'
              }
            </Typography>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default AuthLayout;
