/**
 * Dashboard Page component
 * صفحة لوحة التحكم
 */

import React, { useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  LinearProgress,
  IconButton,
} from '@mui/material';
import {
  People,
  Analytics,
  Assignment,
  Warning,
  Add,
  TrendingUp,
  Schedule,
  Visibility,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../../store/store';
import { useI18n } from '../../i18n/useI18n';

// Mock data for demonstration
const mockStats = {
  totalPatients: 156,
  totalAnalyses: 342,
  pendingReviews: 8,
  urgentCases: 3,
};

const mockRecentAnalyses = [
  {
    id: 1,
    patientName: 'أحمد محمد',
    patientNameFr: '<PERSON>',
    date: '2024-01-15',
    pathology: 'caries',
    severity: 'medium',
    reviewed: false,
  },
  {
    id: 2,
    patientName: 'فاطمة علي',
    patientNameFr: 'Fatma Ali',
    date: '2024-01-14',
    pathology: 'infection',
    severity: 'high',
    reviewed: true,
  },
  {
    id: 3,
    patientName: 'محمد السالمي',
    patientNameFr: 'Mohamed Salmi',
    date: '2024-01-13',
    pathology: 'normal',
    severity: 'low',
    reviewed: true,
  },
];

const mockUpcomingAppointments = [
  {
    id: 1,
    patientName: 'سارة بن علي',
    patientNameFr: 'Sara Ben Ali',
    time: '09:00',
    type: 'consultation',
  },
  {
    id: 2,
    patientName: 'يوسف الهادي',
    patientNameFr: 'Youssef Hadi',
    time: '10:30',
    type: 'follow_up',
  },
];

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { t, currentLanguage } = useI18n();
  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Fetch dashboard data
    // dispatch(fetchDashboardStats());
  }, [dispatch]);

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    trend?: number;
  }> = ({ title, value, icon, color, trend }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ bgcolor: color, mr: 2 }}>
            {icon}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h4" component="div" fontWeight="bold">
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
          {trend && (
            <Box sx={{ textAlign: 'right' }}>
              <TrendingUp color={trend > 0 ? 'success' : 'error'} />
              <Typography variant="caption" color={trend > 0 ? 'success.main' : 'error.main'}>
                {trend > 0 ? '+' : ''}{trend}%
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'error';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  const getPathologyName = (pathology: string) => {
    const pathologyNames: Record<string, Record<string, string>> = {
      caries: { ar: 'تسوس', fr: 'Carie', en: 'Caries' },
      infection: { ar: 'عدوى', fr: 'Infection', en: 'Infection' },
      normal: { ar: 'طبيعي', fr: 'Normal', en: 'Normal' },
    };
    return pathologyNames[pathology]?.[currentLanguage] || pathology;
  };

  return (
    <Box>
      {/* Welcome Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          {t('dashboard.welcome', { 
            name: currentLanguage === 'ar' 
              ? (user?.first_name_ar || user?.first_name)
              : user?.first_name 
          })}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('dashboard.todayStats')}
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('dashboard.totalPatients')}
            value={mockStats.totalPatients}
            icon={<People />}
            color="primary.main"
            trend={12}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('dashboard.totalAnalyses')}
            value={mockStats.totalAnalyses}
            icon={<Analytics />}
            color="success.main"
            trend={8}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('dashboard.pendingReviews')}
            value={mockStats.pendingReviews}
            icon={<Assignment />}
            color="warning.main"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('dashboard.urgentCases')}
            value={mockStats.urgentCases}
            icon={<Warning />}
            color="error.main"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                {t('dashboard.quickActions')}
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => navigate('/patients/new')}
                  fullWidth
                >
                  {t('dashboard.addNewPatient')}
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Analytics />}
                  onClick={() => navigate('/analysis')}
                  fullWidth
                >
                  {t('dashboard.analyzeXray')}
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Assignment />}
                  onClick={() => navigate('/reports')}
                  fullWidth
                >
                  {t('dashboard.viewReports')}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Analyses */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  {t('dashboard.recentAnalyses')}
                </Typography>
                <IconButton onClick={() => navigate('/analysis')}>
                  <Visibility />
                </IconButton>
              </Box>
              <List dense>
                {mockRecentAnalyses.map((analysis) => (
                  <ListItem key={analysis.id} sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: getSeverityColor(analysis.severity) + '.main' }}>
                        <Analytics />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        currentLanguage === 'ar' 
                          ? analysis.patientName 
                          : analysis.patientNameFr
                      }
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {analysis.date}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                            <Chip
                              label={getPathologyName(analysis.pathology)}
                              size="small"
                              color={getSeverityColor(analysis.severity) as any}
                            />
                            {!analysis.reviewed && (
                              <Chip
                                label={t('analysis.notReviewed')}
                                size="small"
                                variant="outlined"
                                color="warning"
                              />
                            )}
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Upcoming Appointments */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  {t('dashboard.upcomingAppointments')}
                </Typography>
                <IconButton>
                  <Schedule />
                </IconButton>
              </Box>
              <List dense>
                {mockUpcomingAppointments.map((appointment) => (
                  <ListItem key={appointment.id} sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'info.main' }}>
                        <Schedule />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        currentLanguage === 'ar' 
                          ? appointment.patientName 
                          : appointment.patientNameFr
                      }
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {appointment.time}
                          </Typography>
                          <Chip
                            label={appointment.type}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
