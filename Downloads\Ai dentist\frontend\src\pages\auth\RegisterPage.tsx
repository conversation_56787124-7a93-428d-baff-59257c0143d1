/**
 * Register Page component
 * صفحة التسجيل
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Link,
  Alert,
  InputAdornment,
  IconButton,
  Grid,
  CircularProgress,
  MenuItem,
} from '@mui/material';
import { Visibility, VisibilityOff, Person, Email, Phone, Business } from '@mui/icons-material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';
import { AppDispatch, RootState } from '../../store/store';
import { registerUser, clearError } from '../../store/slices/authSlice';
import { useI18n } from '../../i18n/useI18n';

interface RegisterFormData {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  firstNameAr: string;
  lastNameAr: string;
  phone: string;
  licenseNumber: string;
  specialization: string;
  clinicName: string;
  clinicAddress: string;
  preferredLanguage: string;
}

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useI18n();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterFormData>({
    defaultValues: {
      email: '',
      username: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      firstNameAr: '',
      lastNameAr: '',
      phone: '',
      licenseNumber: '',
      specialization: '',
      clinicName: '',
      clinicAddress: '',
      preferredLanguage: 'ar',
    },
  });

  const password = watch('password');

  useEffect(() => {
    // Clear any previous errors when component mounts
    dispatch(clearError());
  }, [dispatch]);

  const onSubmit = (data: RegisterFormData) => {
    dispatch(registerUser(data));
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const specializations = [
    'General Dentistry',
    'Orthodontics',
    'Oral Surgery',
    'Endodontics',
    'Periodontics',
    'Prosthodontics',
    'Pediatric Dentistry',
    'Oral Pathology',
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ width: '100%' }}>
      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        textAlign="center"
        sx={{ mb: 3, fontWeight: 600 }}
      >
        {t('auth.registerTitle')}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {typeof error === 'string' ? error : t('errors.general')}
        </Alert>
      )}

      <Grid container spacing={2}>
        {/* Email */}
        <Grid item xs={12}>
          <Controller
            name="email"
            control={control}
            rules={{
              required: t('common.required'),
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: t('errors.validationError'),
              },
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.email')}
                type="email"
                error={!!errors.email}
                helperText={errors.email?.message}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Email />
                    </InputAdornment>
                  ),
                }}
                autoComplete="email"
              />
            )}
          />
        </Grid>

        {/* Username */}
        <Grid item xs={12}>
          <Controller
            name="username"
            control={control}
            rules={{
              required: t('common.required'),
              minLength: {
                value: 3,
                message: t('errors.validationError'),
              },
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.username')}
                error={!!errors.username}
                helperText={errors.username?.message}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Person />
                    </InputAdornment>
                  ),
                }}
                autoComplete="username"
              />
            )}
          />
        </Grid>

        {/* Password */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="password"
            control={control}
            rules={{
              required: t('common.required'),
              minLength: {
                value: 8,
                message: t('errors.passwordTooWeak'),
              },
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.password')}
                type={showPassword ? 'text' : 'password'}
                error={!!errors.password}
                helperText={errors.password?.message}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                autoComplete="new-password"
              />
            )}
          />
        </Grid>

        {/* Confirm Password */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="confirmPassword"
            control={control}
            rules={{
              required: t('common.required'),
              validate: (value) =>
                value === password || t('errors.validationError'),
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.confirmPassword')}
                type={showConfirmPassword ? 'text' : 'password'}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword?.message}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleToggleConfirmPasswordVisibility}
                        edge="end"
                      >
                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                autoComplete="new-password"
              />
            )}
          />
        </Grid>

        {/* First Name */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="firstName"
            control={control}
            rules={{ required: t('common.required') }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.firstName')}
                error={!!errors.firstName}
                helperText={errors.firstName?.message}
                autoComplete="given-name"
              />
            )}
          />
        </Grid>

        {/* Last Name */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="lastName"
            control={control}
            rules={{ required: t('common.required') }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.lastName')}
                error={!!errors.lastName}
                helperText={errors.lastName?.message}
                autoComplete="family-name"
              />
            )}
          />
        </Grid>

        {/* Arabic First Name */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="firstNameAr"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.firstNameAr')}
                error={!!errors.firstNameAr}
                helperText={errors.firstNameAr?.message}
              />
            )}
          />
        </Grid>

        {/* Arabic Last Name */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="lastNameAr"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.lastNameAr')}
                error={!!errors.lastNameAr}
                helperText={errors.lastNameAr?.message}
              />
            )}
          />
        </Grid>

        {/* Phone */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="phone"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('common.phone')}
                error={!!errors.phone}
                helperText={errors.phone?.message}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Phone />
                    </InputAdornment>
                  ),
                }}
                autoComplete="tel"
              />
            )}
          />
        </Grid>

        {/* License Number */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="licenseNumber"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.licenseNumber')}
                error={!!errors.licenseNumber}
                helperText={errors.licenseNumber?.message}
              />
            )}
          />
        </Grid>

        {/* Specialization */}
        <Grid item xs={12}>
          <Controller
            name="specialization"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                select
                label={t('auth.specialization')}
                error={!!errors.specialization}
                helperText={errors.specialization?.message}
              >
                {specializations.map((spec) => (
                  <MenuItem key={spec} value={spec}>
                    {spec}
                  </MenuItem>
                ))}
              </TextField>
            )}
          />
        </Grid>

        {/* Clinic Name */}
        <Grid item xs={12}>
          <Controller
            name="clinicName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label={t('auth.clinicName')}
                error={!!errors.clinicName}
                helperText={errors.clinicName?.message}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Business />
                    </InputAdornment>
                  ),
                }}
              />
            )}
          />
        </Grid>

        {/* Clinic Address */}
        <Grid item xs={12}>
          <Controller
            name="clinicAddress"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                multiline
                rows={2}
                label={t('auth.clinicAddress')}
                error={!!errors.clinicAddress}
                helperText={errors.clinicAddress?.message}
              />
            )}
          />
        </Grid>

        {/* Preferred Language */}
        <Grid item xs={12}>
          <Controller
            name="preferredLanguage"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                select
                label={t('common.language')}
                error={!!errors.preferredLanguage}
                helperText={errors.preferredLanguage?.message}
              >
                <MenuItem value="ar">العربية</MenuItem>
                <MenuItem value="fr">Français</MenuItem>
                <MenuItem value="en">English</MenuItem>
              </TextField>
            )}
          />
        </Grid>
      </Grid>

      <Button
        type="submit"
        fullWidth
        variant="contained"
        size="large"
        disabled={isLoading}
        sx={{
          mt: 3,
          mb: 2,
          py: 1.5,
          fontSize: '1.1rem',
          fontWeight: 600,
        }}
      >
        {isLoading ? (
          <CircularProgress size={24} color="inherit" />
        ) : (
          t('auth.signUp')
        )}
      </Button>

      <Box sx={{ textAlign: 'center', mt: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {t('auth.alreadyHaveAccount')}{' '}
          <Link component={RouterLink} to="/auth/login" variant="body2">
            {t('auth.signIn')}
          </Link>
        </Typography>
      </Box>
    </Box>
  );
};

export default RegisterPage;
