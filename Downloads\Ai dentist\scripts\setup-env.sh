#!/bin/bash

# Environment setup script for AI Dental Assistant
# سكريبت إعداد البيئة لمساعد طبيب الأسنان الذكي

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log "🔧 Setting up AI Dental Assistant development environment"
log "🦷 إعداد بيئة تطوير مساعد طبيب الأسنان الذكي"

# Create environment files
log "📝 Creating environment files..."

# Backend .env
if [ ! -f "backend/.env" ]; then
    cat > backend/.env << EOF
# Database Configuration
DATABASE_URL=postgresql://dental_user:dental_pass@localhost:5432/dental_ai_db
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=dev-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application
ENVIRONMENT=development
DEBUG=true
APP_NAME=AI Dental Assistant
VERSION=1.0.0

# File Upload
MAX_FILE_SIZE=52428800
UPLOAD_DIR=uploads
ALLOWED_IMAGE_EXTENSIONS=[".jpg", ".jpeg", ".png", ".dcm", ".dicom"]

# AI Model
MODEL_PATH=models/dental_resnet.pth
MODEL_DEVICE=cpu
CONFIDENCE_THRESHOLD=0.7

# Localization
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=["ar", "fr", "en"]

# CORS
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/dental_ai.log

# INPDP Compliance
DATA_RETENTION_DAYS=2555
ANONYMIZATION_ENABLED=true
AUDIT_LOG_ENABLED=true
EOF
    success "Created backend/.env"
else
    warning "backend/.env already exists, skipping"
fi

# Frontend .env
if [ ! -f "frontend/.env" ]; then
    cat > frontend/.env << EOF
# API Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENVIRONMENT=development

# Application
REACT_APP_NAME=AI Dental Assistant
REACT_APP_VERSION=1.0.0

# Localization
REACT_APP_DEFAULT_LANGUAGE=ar
REACT_APP_SUPPORTED_LANGUAGES=ar,fr,en

# Features
REACT_APP_ENABLE_TELEMEDICINE=true
REACT_APP_ENABLE_OFFLINE_MODE=true
REACT_APP_ENABLE_PWA=true

# Development
GENERATE_SOURCEMAP=true
REACT_APP_DEBUG=true
EOF
    success "Created frontend/.env"
else
    warning "frontend/.env already exists, skipping"
fi

# Create necessary directories
log "📁 Creating necessary directories..."

directories=(
    "backend/uploads"
    "backend/models"
    "backend/logs"
    "backend/static"
    "backend/temp"
    "backups"
    "logs"
)

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        success "Created directory: $dir"
    else
        warning "Directory already exists: $dir"
    fi
done

# Set permissions
log "🔒 Setting permissions..."
chmod -R 755 backend/uploads 2>/dev/null || true
chmod -R 755 backend/logs 2>/dev/null || true
chmod -R 755 logs 2>/dev/null || true

# Create gitignore entries
log "📝 Updating .gitignore..."

if [ ! -f ".gitignore" ]; then
    cat > .gitignore << EOF
# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/
backend/logs/

# Uploads and temporary files
backend/uploads/
backend/temp/
uploads/
temp/

# AI Models (large files)
*.pth
*.pkl
*.h5
models/

# Database
*.db
*.sqlite3

# Backups
backups/

# Build outputs
build/
dist/
*.egg-info/

# Coverage reports
htmlcov/
.coverage
.pytest_cache/

# Docker
.dockerignore

# Jupyter Notebooks
.ipynb_checkpoints/

# Medical data (IMPORTANT: Never commit patient data)
patient_data/
medical_images/
dicom_files/
EOF
    success "Created .gitignore"
else
    warning ".gitignore already exists, please review manually"
fi

# Create development docker-compose override
if [ ! -f "docker-compose.override.yml" ]; then
    cat > docker-compose.override.yml << EOF
# Development overrides for docker-compose.yml
version: '3.8'

services:
  backend:
    volumes:
      - ./backend:/app
    environment:
      - DEBUG=true
      - RELOAD_ON_CHANGE=true
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  frontend:
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - CHOKIDAR_USEPOLLING=true
      - FAST_REFRESH=true
    stdin_open: true
    tty: true

  postgres:
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=dental_ai_db
      - POSTGRES_USER=dental_user
      - POSTGRES_PASSWORD=dental_pass

  redis:
    ports:
      - "6379:6379"
EOF
    success "Created docker-compose.override.yml for development"
else
    warning "docker-compose.override.yml already exists, skipping"
fi

# Create a simple health check script
cat > scripts/health-check.sh << 'EOF'
#!/bin/bash

# Simple health check script
echo "🏥 Health Check for AI Dental Assistant"

# Check if containers are running
echo "📦 Container Status:"
docker-compose ps

echo ""
echo "🌐 Service Health:"

# Check backend
if curl -f http://localhost:8000/health &> /dev/null; then
    echo "✅ Backend API: Healthy"
else
    echo "❌ Backend API: Not responding"
fi

# Check frontend
if curl -f http://localhost:3000 &> /dev/null; then
    echo "✅ Frontend: Healthy"
else
    echo "❌ Frontend: Not responding"
fi

# Check database
if docker-compose exec -T postgres pg_isready -U dental_user -d dental_ai_db &> /dev/null; then
    echo "✅ PostgreSQL: Healthy"
else
    echo "❌ PostgreSQL: Not responding"
fi

# Check Redis
if docker-compose exec -T redis redis-cli ping &> /dev/null; then
    echo "✅ Redis: Healthy"
else
    echo "❌ Redis: Not responding"
fi
EOF

chmod +x scripts/health-check.sh
success "Created scripts/health-check.sh"

# Final instructions
log "✅ Environment setup completed!"
log ""
log "📋 Next steps:"
log "1. Review and customize the .env files if needed"
log "2. Start the development environment:"
log "   docker-compose up -d"
log ""
log "3. Run health checks:"
log "   ./scripts/health-check.sh"
log ""
log "4. Access the application:"
log "   - Frontend: http://localhost:3000"
log "   - Backend API: http://localhost:8000"
log "   - API Docs: http://localhost:8000/docs"
log ""
log "🦷 Happy coding! / برمجة سعيدة!"

exit 0
