/**
 * Patient Detail Page component
 * صفحة تفاصيل المريض
 */

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
} from '@mui/material';
import { useParams } from 'react-router-dom';
import { useI18n } from '../../i18n/useI18n';

const PatientDetailPage: React.FC = () => {
  const { patientId } = useParams<{ patientId: string }>();
  const { t } = useI18n();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        {t('patients.patientDetails')}
      </Typography>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Patient ID: {patientId}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('patients.patientDetails')} - {t('common.loading')}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default PatientDetailPage;
