#!/bin/bash

# Deployment script for AI Dental Assistant
# سكريبت النشر لمساعد طبيب الأسنان الذكي

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ai-dental-assistant"
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="backups"
LOG_FILE="deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a $LOG_FILE
}

# Check if environment is provided
if [ $# -eq 0 ]; then
    error "Usage: $0 <environment> [options]
    
Environments:
  development  - Development environment
  staging      - Staging environment  
  production   - Production environment
  
Options:
  --backup     - Create backup before deployment
  --migrate    - Run database migrations
  --build      - Force rebuild of containers
  --no-cache   - Build without cache
  
Examples:
  $0 development
  $0 production --backup --migrate
  $0 staging --build --no-cache"
fi

ENVIRONMENT=$1
shift

# Parse options
BACKUP=false
MIGRATE=false
BUILD=false
NO_CACHE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --backup)
            BACKUP=true
            shift
            ;;
        --migrate)
            MIGRATE=true
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        *)
            warning "Unknown option: $1"
            shift
            ;;
    esac
done

# Set environment-specific configuration
case $ENVIRONMENT in
    development)
        DOCKER_COMPOSE_FILE="docker-compose.yml"
        ;;
    staging)
        DOCKER_COMPOSE_FILE="docker-compose.staging.yml"
        ;;
    production)
        DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
        ;;
    *)
        error "Invalid environment: $ENVIRONMENT. Use development, staging, or production."
        ;;
esac

log "🚀 Starting deployment for $ENVIRONMENT environment"
log "📋 Configuration:"
log "   - Environment: $ENVIRONMENT"
log "   - Docker Compose: $DOCKER_COMPOSE_FILE"
log "   - Backup: $BACKUP"
log "   - Migrate: $MIGRATE"
log "   - Build: $BUILD"
log "   - No Cache: $NO_CACHE"

# Check prerequisites
log "🔍 Checking prerequisites..."

if ! command -v docker &> /dev/null; then
    error "Docker is not installed or not in PATH"
fi

if ! command -v docker-compose &> /dev/null; then
    error "Docker Compose is not installed or not in PATH"
fi

if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
    error "Docker Compose file not found: $DOCKER_COMPOSE_FILE"
fi

success "Prerequisites check passed"

# Create backup if requested
if [ "$BACKUP" = true ]; then
    log "💾 Creating backup..."
    
    # Create backup directory
    mkdir -p $BACKUP_DIR
    
    # Backup database
    BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if docker-compose -f $DOCKER_COMPOSE_FILE ps postgres | grep -q "Up"; then
        log "Creating database backup: $BACKUP_FILE"
        docker-compose -f $DOCKER_COMPOSE_FILE exec -T postgres pg_dump -U dental_user dental_ai_db > $BACKUP_FILE
        
        if [ $? -eq 0 ]; then
            success "Database backup created: $BACKUP_FILE"
        else
            error "Failed to create database backup"
        fi
    else
        warning "PostgreSQL container is not running, skipping database backup"
    fi
    
    # Backup uploaded files
    if [ -d "uploads" ]; then
        UPLOADS_BACKUP="$BACKUP_DIR/uploads_$(date +%Y%m%d_%H%M%S).tar.gz"
        tar -czf $UPLOADS_BACKUP uploads/
        success "Uploads backup created: $UPLOADS_BACKUP"
    fi
fi

# Stop existing containers
log "🛑 Stopping existing containers..."
docker-compose -f $DOCKER_COMPOSE_FILE down

# Build containers if requested
if [ "$BUILD" = true ]; then
    log "🔨 Building containers..."
    
    BUILD_ARGS=""
    if [ "$NO_CACHE" = true ]; then
        BUILD_ARGS="--no-cache"
    fi
    
    docker-compose -f $DOCKER_COMPOSE_FILE build $BUILD_ARGS
    
    if [ $? -eq 0 ]; then
        success "Containers built successfully"
    else
        error "Failed to build containers"
    fi
fi

# Start containers
log "🚀 Starting containers..."
docker-compose -f $DOCKER_COMPOSE_FILE up -d

if [ $? -eq 0 ]; then
    success "Containers started successfully"
else
    error "Failed to start containers"
fi

# Wait for services to be ready
log "⏳ Waiting for services to be ready..."

# Wait for database
log "Waiting for PostgreSQL..."
timeout=60
while ! docker-compose -f $DOCKER_COMPOSE_FILE exec -T postgres pg_isready -U dental_user -d dental_ai_db &> /dev/null; do
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        error "PostgreSQL failed to start within 60 seconds"
    fi
done
success "PostgreSQL is ready"

# Wait for backend
log "Waiting for backend API..."
timeout=60
while ! curl -f http://localhost:8000/health &> /dev/null; do
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        error "Backend API failed to start within 60 seconds"
    fi
done
success "Backend API is ready"

# Run database migrations if requested
if [ "$MIGRATE" = true ]; then
    log "🗄️ Running database migrations..."
    
    docker-compose -f $DOCKER_COMPOSE_FILE exec backend alembic upgrade head
    
    if [ $? -eq 0 ]; then
        success "Database migrations completed"
    else
        error "Database migrations failed"
    fi
fi

# Health checks
log "🏥 Running health checks..."

# Check all services
SERVICES=("postgres" "redis" "backend" "frontend")

for service in "${SERVICES[@]}"; do
    if docker-compose -f $DOCKER_COMPOSE_FILE ps $service | grep -q "Up"; then
        success "$service is running"
    else
        error "$service is not running"
    fi
done

# Check API endpoints
log "Testing API endpoints..."

# Test health endpoint
if curl -f http://localhost:8000/health &> /dev/null; then
    success "Health endpoint is responding"
else
    error "Health endpoint is not responding"
fi

# Test API info endpoint
if curl -f http://localhost:8000/api/v1/info &> /dev/null; then
    success "API info endpoint is responding"
else
    warning "API info endpoint is not responding"
fi

# Display deployment summary
log "📊 Deployment Summary:"
log "   - Environment: $ENVIRONMENT"
log "   - Status: SUCCESS ✅"
log "   - Frontend URL: http://localhost:3000"
log "   - Backend URL: http://localhost:8000"
log "   - API Docs: http://localhost:8000/docs"

# Display running containers
log "🐳 Running containers:"
docker-compose -f $DOCKER_COMPOSE_FILE ps

# Show logs for any failed containers
FAILED_CONTAINERS=$(docker-compose -f $DOCKER_COMPOSE_FILE ps --filter "status=exited" --format "table {{.Service}}")
if [ ! -z "$FAILED_CONTAINERS" ]; then
    warning "Some containers have exited. Check logs:"
    echo "$FAILED_CONTAINERS"
fi

# Final success message
success "🎉 Deployment completed successfully!"
success "🦷 مساعد طبيب الأسنان الذكي جاهز للاستخدام!"

# Show next steps
log "📝 Next steps:"
log "   1. Access the application at http://localhost:3000"
log "   2. Check API documentation at http://localhost:8000/docs"
log "   3. Monitor logs: docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
log "   4. Create admin user if needed"

exit 0
