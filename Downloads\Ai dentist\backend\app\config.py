"""
Configuration settings for AI Dental Assistant
إعدادات التكوين لمساعد طبيب الأسنان الذكي
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    APP_NAME: str = "AI Dental Assistant"
    APP_NAME_AR: str = "مساعد طبيب الأسنان الذكي"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # API
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Database
    DATABASE_URL: str = "postgresql://dental_user:dental_pass@localhost:5432/dental_ai_db"
    DATABASE_ECHO: bool = False
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # CORS
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",  # React development server
        "http://localhost:8080",  # Alternative frontend port
        "https://dental-ai.tn",   # Production domain
    ]
    
    # File Upload
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    UPLOAD_DIR: str = "uploads"
    ALLOWED_IMAGE_EXTENSIONS: List[str] = [".jpg", ".jpeg", ".png", ".dcm", ".dicom"]
    
    # AI Model
    MODEL_PATH: str = "models/dental_resnet.pth"
    MODEL_DEVICE: str = "cpu"  # or "cuda" if GPU available
    CONFIDENCE_THRESHOLD: float = 0.7
    
    # Localization
    DEFAULT_LANGUAGE: str = "ar"  # Arabic as default for Tunisia
    SUPPORTED_LANGUAGES: List[str] = ["ar", "fr", "en"]
    
    # Security
    BCRYPT_ROUNDS: int = 12
    JWT_ALGORITHM: str = "HS256"
    
    # Email (for notifications)
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_TLS: bool = True
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/dental_ai.log"
    
    # INPDP Compliance
    DATA_RETENTION_DAYS: int = 2555  # 7 years as per Tunisian medical records law
    ANONYMIZATION_ENABLED: bool = True
    AUDIT_LOG_ENABLED: bool = True
    
    # Telemedicine
    VIDEO_CALL_ENABLED: bool = True
    MAX_CONSULTATION_DURATION: int = 60  # minutes
    
    # Offline Mode
    OFFLINE_MODE_ENABLED: bool = True
    SYNC_INTERVAL_MINUTES: int = 30
    
    # Performance
    MAX_CONCURRENT_ANALYSES: int = 5
    CACHE_TTL_SECONDS: int = 3600  # 1 hour
    
    # Monitoring
    METRICS_ENABLED: bool = True
    HEALTH_CHECK_INTERVAL: int = 60  # seconds
    
    # Tunisian Specific
    CNAM_INTEGRATION_ENABLED: bool = False  # Enable when CNAM API is available
    TUNISIAN_ID_VALIDATION: bool = True
    ARABIC_RTL_SUPPORT: bool = True
    
    # Development
    RELOAD_ON_CHANGE: bool = True
    SHOW_SQL_QUERIES: bool = False
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()


# Ensure required directories exist
def create_directories():
    """Create necessary directories if they don't exist"""
    directories = [
        settings.UPLOAD_DIR,
        "logs",
        "static",
        "models",
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


# Language configuration
LANGUAGE_CONFIG = {
    "ar": {
        "name": "العربية",
        "code": "ar",
        "direction": "rtl",
        "locale": "ar-TN"
    },
    "fr": {
        "name": "Français",
        "code": "fr", 
        "direction": "ltr",
        "locale": "fr-TN"
    },
    "en": {
        "name": "English",
        "code": "en",
        "direction": "ltr", 
        "locale": "en-US"
    }
}

# Medical terminology mapping
MEDICAL_TERMS = {
    "caries": {
        "ar": "تسوس",
        "fr": "carie",
        "en": "caries"
    },
    "infection": {
        "ar": "عدوى",
        "fr": "infection", 
        "en": "infection"
    },
    "fracture": {
        "ar": "كسر",
        "fr": "fracture",
        "en": "fracture"
    },
    "cyst": {
        "ar": "كيس",
        "fr": "kyste",
        "en": "cyst"
    },
    "tumor": {
        "ar": "ورم",
        "fr": "tumeur",
        "en": "tumor"
    }
}

# Pathology severity levels
SEVERITY_LEVELS = {
    "low": {
        "ar": "منخفض",
        "fr": "faible",
        "en": "low",
        "color": "#4CAF50"
    },
    "medium": {
        "ar": "متوسط", 
        "fr": "moyen",
        "en": "medium",
        "color": "#FF9800"
    },
    "high": {
        "ar": "عالي",
        "fr": "élevé", 
        "en": "high",
        "color": "#F44336"
    },
    "critical": {
        "ar": "حرج",
        "fr": "critique",
        "en": "critical",
        "color": "#9C27B0"
    }
}

# Initialize directories on import
create_directories()
