<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="AI Dental Assistant - مساعد طبيب الأسنان الذكي"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts for Arabic and Latin text -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <title>AI Dental Assistant - مساعد طبيب الأسنان الذكي</title>
    
    <style>
      /* Global styles for RTL support */
      body {
        margin: 0;
        font-family: 'Cairo', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        direction: rtl;
      }
      
      /* LTR support for French/English */
      [dir="ltr"] {
        direction: ltr;
        font-family: 'Roboto', 'Cairo', sans-serif;
      }
      
      code {
        font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
          monospace;
      }
      
      /* Loading spinner */
      .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background-color: #f5f5f5;
      }
      
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e3f2fd;
        border-top: 4px solid #1976d2;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Dental theme colors */
      :root {
        --primary-color: #1976d2;
        --secondary-color: #dc004e;
        --success-color: #2e7d32;
        --warning-color: #ed6c02;
        --error-color: #d32f2f;
        --background-color: #fafafa;
        --surface-color: #ffffff;
        --text-primary: #212121;
        --text-secondary: #757575;
      }
    </style>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 50px;">
        <h2>يجب تفعيل JavaScript لتشغيل هذا التطبيق</h2>
        <p>You need to enable JavaScript to run this app.</p>
      </div>
    </noscript>
    
    <div id="root">
      <!-- Loading spinner while React loads -->
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
    </div>
    
    <!-- Service Worker for PWA functionality -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
