# 🗂️ Data Organization Guide for AI Dental Assistant
# دليل تنظيم البيانات لمساعد طبيب الأسنان الذكي

## Overview / نظرة عامة

This guide will help you organize your unstructured dental X-ray images into the proper format required for training the AI models.

سيساعدك هذا الدليل في تنظيم صور الأشعة السينية للأسنان غير المنظمة إلى التنسيق المطلوب لتدريب نماذج الذكاء الاصطناعي.

## 📁 Input Structure / هيكل الإدخال

Place all your dental X-ray images in a folder named `sets`. The images can be organized in any way within this folder:

```
sets/
├── folder1/
│   ├── image1.jpg
│   ├── image2.png
│   └── subfolder/
│       └── image3.dcm
├── folder2/
│   ├── caries_case1.jpg
│   └── infection_case2.png
├── random_images/
│   ├── xray001.jpg
│   ├── xray002.jpg
│   └── ...
└── any_other_organization/
    └── dental_images/
        ├── normal_tooth.png
        └── fractured_tooth.jpg
```

## 🎯 Output Structure / هيكل الإخراج

The script will organize your data into this structure:

```
ai-models/data/
├── train/
│   ├── images/           # 70% of your images
│   │   ├── train_000001.jpg
│   │   ├── train_000002.png
│   │   └── ...
│   └── masks/            # (Optional) Segmentation masks
├── val/
│   ├── images/           # 20% of your images
│   │   ├── val_000001.jpg
│   │   └── ...
│   └── masks/
├── test/
│   ├── images/           # 10% of your images
│   │   ├── test_000001.jpg
│   │   └── ...
│   └── masks/
├── train_annotations.json
├── val_annotations.json
├── test_annotations.json
└── organization_report.json
```

## 🚀 How to Use / كيفية الاستخدام

### Method 1: Simple Batch Script (Windows)

1. **Prepare your data:**
   - Create a folder named `sets` in the project root
   - Copy all your dental X-ray images into the `sets` folder
   - Images can be in any subfolder structure

2. **Run the organization:**
   ```bash
   # Double-click the batch file or run in command prompt
   scripts\organize_data.bat
   ```

### Method 2: Python Script (All Platforms)

1. **Prepare your data** (same as above)

2. **Run the Python script:**
   ```bash
   cd "Downloads\Ai dentist"
   python scripts\organize_data.py
   ```

3. **Follow the prompts:**
   - Enter the path to your data folder (or press Enter for 'sets')
   - Confirm the organization process

## 🔍 Automatic Classification / التصنيف التلقائي

The script automatically classifies images based on filename and folder names using these keywords:

### Supported Pathologies:

| Pathology | English Keywords | Arabic Keywords | French Keywords |
|-----------|------------------|-----------------|-----------------|
| **Caries** | caries, cavity, decay | تسوس، نخر | carie, cavité |
| **Infection** | infection, abscess, pus | عدوى، التهاب، خراج | infection, abcès |
| **Fracture** | fracture, break, crack | كسر، شرخ، انكسار | fracture, cassure |
| **Cyst** | cyst, cystic | كيس، تكيس | kyste |
| **Tumor** | tumor, mass, growth | ورم، كتلة | tumeur, masse |
| **Normal** | normal, healthy, clean | طبيعي، سليم، صحي | normal, sain |

### Examples:
- `caries_case1.jpg` → Classified as **caries**
- `folder_infection/xray.png` → Classified as **infection**
- `normal_tooth.dcm` → Classified as **normal**
- `random_name.jpg` → Classified as **normal** (default)

## 📊 Generated Files / الملفات المُنتجة

### 1. Annotation Files
Each split gets a JSON annotation file with this structure:
```json
[
  {
    "image_id": "train_000001",
    "image_path": "images/train_000001.jpg",
    "original_path": "sets/folder1/original_image.jpg",
    "pathologies": [
      {
        "name": "caries",
        "confidence": 1.0,
        "location": {"x": 0, "y": 0, "width": 0, "height": 0}
      }
    ],
    "severity_score": 0.5,
    "mask_path": null,
    "created_at": "2025-05-28T16:30:00"
  }
]
```

### 2. Organization Report
A comprehensive report showing:
- Total images processed
- Distribution across train/val/test splits
- Pathology counts for each split
- Organization statistics

## ⚙️ Customization / التخصيص

### Modify Split Ratios
Edit the script or use the configuration file to change data splits:
- Default: 70% train, 20% validation, 10% test
- Minimum recommended: 60% train, 25% validation, 15% test

### Add Custom Keywords
Edit `scripts/data_config.json` to add your own classification keywords:
```json
{
  "pathology_classification": {
    "keywords": {
      "your_pathology": {
        "english": ["keyword1", "keyword2"],
        "arabic": ["كلمة1", "كلمة2"],
        "french": ["mot1", "mot2"]
      }
    }
  }
}
```

## 📋 Supported Formats / التنسيقات المدعومة

- **JPEG**: .jpg, .jpeg
- **PNG**: .png
- **Bitmap**: .bmp
- **TIFF**: .tiff
- **DICOM**: .dcm, .dicom

## ✅ Quality Checks / فحوصات الجودة

The script automatically:
- ✅ Validates image files can be loaded
- ✅ Filters out corrupted images
- ✅ Checks minimum image dimensions
- ✅ Removes duplicate images
- ✅ Generates quality report

## 🔧 Troubleshooting / استكشاف الأخطاء

### Common Issues:

1. **"sets folder not found"**
   - Create a `sets` folder in the project root
   - Make sure the folder name is exactly `sets`

2. **"No images found"**
   - Check that your images have supported extensions
   - Verify images are not corrupted

3. **"Permission denied"**
   - Run as administrator (Windows)
   - Check folder permissions

4. **"Python not found"**
   - Install Python 3.9+
   - Add Python to system PATH

### Getting Help:
- Check the generated `organization_report.json` for details
- Look at the console output for specific error messages
- Ensure all dependencies are installed

## 🎯 Next Steps / الخطوات التالية

After organizing your data:

1. **Review the organization report**
2. **Check the generated annotations**
3. **Start training your AI models:**
   ```bash
   cd ai-models
   python training/train_comprehensive_model.py
   ```

## 📞 Support / الدعم

For issues or questions:
- Check the organization report for details
- Review console output for error messages
- Ensure your images are valid dental X-rays

---

**Happy Training! / تدريب سعيد!** 🦷🤖
