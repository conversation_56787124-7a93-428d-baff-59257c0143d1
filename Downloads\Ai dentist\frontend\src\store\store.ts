/**
 * Redux store configuration for AI Dental Assistant
 * تكوين متجر Redux لمساعد طبيب الأسنان الذكي
 */

import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import slices
import authSlice from './slices/authSlice';
import patientsSlice from './slices/patientsSlice';
import analysisSlice from './slices/analysisSlice';
import uiSlice from './slices/uiSlice';
import settingsSlice from './slices/settingsSlice';

// Persist configuration
const persistConfig = {
  key: 'dental-ai-root',
  storage,
  whitelist: ['auth', 'settings'], // Only persist auth and settings
};

// Root reducer
const rootReducer = combineReducers({
  auth: authSlice,
  patients: patientsSlice,
  analysis: analysisSlice,
  ui: uiSlice,
  settings: settingsSlice,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// Persistor
export const persistor = persistStore(store);

// Types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
