/**
 * Telemedicine Page component
 * صفحة الطب عن بُعد
 */

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
} from '@mui/material';
import { VideoCall } from '@mui/icons-material';
import { useI18n } from '../../i18n/useI18n';

const TelemedicinePage: React.FC = () => {
  const { t } = useI18n();

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          {t('telemedicine.title')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<VideoCall />}
          size="large"
        >
          {t('telemedicine.startConsultation')}
        </Button>
      </Box>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('common.loading')}...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('telemedicine.title')} - {t('common.loading')}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default TelemedicinePage;
