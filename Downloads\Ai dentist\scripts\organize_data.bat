@echo off
echo 🦷 AI Dental Assistant - Data Organization Tool
echo ================================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Check if sets folder exists
if not exist "sets" (
    echo ❌ 'sets' folder not found!
    echo Please create a 'sets' folder and put your dental X-ray images there
    echo The folder can contain subfolders with images in any organization
    pause
    exit /b 1
)

echo ✅ Found 'sets' folder
echo 🔍 Starting data organization...

REM Run the Python script
python scripts\organize_data.py

echo.
echo ✅ Data organization completed!
echo 📁 Check the 'ai-models\data' folder for organized data
echo.
pause
