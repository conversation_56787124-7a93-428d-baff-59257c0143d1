"""
Enhanced analysis schemas for comprehensive dental pathology detection
مخططات التحليل المحسنة للكشف الشامل عن الأمراض السنية
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class SeverityLevel(str, Enum):
    """Severity levels for pathologies"""
    LOW = "low"
    MILD = "mild"
    MEDIUM = "medium"
    MODERATE = "moderate"
    HIGH = "high"
    SEVERE = "severe"
    CRITICAL = "critical"


class RiskLevel(str, Enum):
    """Overall risk assessment levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ProgressionStatus(str, Enum):
    """Temporal progression status"""
    IMPROVED = "improved"
    STABLE = "stable"
    WORSENED = "worsened"


class ComprehensivePathologyResult(BaseModel):
    """
    Comprehensive pathology detection result
    نتيجة الكشف الشامل عن الأمراض
    """
    pathology_id: str = Field(..., description="Unique pathology identifier")
    name: str = Field(..., description="Pathology name in requested language")
    probability: float = Field(..., ge=0.0, le=1.0, description="Detection probability")
    confidence: float = Field(..., ge=0.0, le=1.0, description="AI confidence score")
    severity: SeverityLevel = Field(..., description="Severity level")
    urgent: bool = Field(..., description="Requires urgent attention")
    recommendations: str = Field(..., description="Clinical recommendations")
    
    class Config:
        schema_extra = {
            "example": {
                "pathology_id": "caries_deep",
                "name": "تسوس عميق",
                "probability": 0.85,
                "confidence": 0.92,
                "severity": "high",
                "urgent": True,
                "recommendations": "علاج عصب أو حشوة عميقة"
            }
        }


class VisualizationData(BaseModel):
    """
    Visualization data for heatmaps and overlays
    بيانات التصور للخرائط الحرارية والطبقات
    """
    localization_heatmap: Optional[str] = Field(None, description="Base64 encoded localization heatmap")
    grad_cam_heatmap: Optional[str] = Field(None, description="Base64 encoded Grad-CAM heatmap")
    original_image: Optional[str] = Field(None, description="Base64 encoded original image")
    error: Optional[str] = Field(None, description="Visualization error message")


class TemporalAnalysisResult(BaseModel):
    """
    Temporal comparison analysis result
    نتيجة تحليل المقارنة الزمنية
    """
    overall_progression: ProgressionStatus = Field(..., description="Overall progression status")
    progression_text: str = Field(..., description="Progression description in requested language")
    new_pathologies: int = Field(..., description="Number of new pathologies detected")
    resolved_pathologies: int = Field(..., description="Number of resolved pathologies")
    progressed_pathologies: int = Field(..., description="Number of worsened pathologies")
    stable_pathologies: int = Field(..., description="Number of stable pathologies")
    
    class Config:
        schema_extra = {
            "example": {
                "overall_progression": "stable",
                "progression_text": "مستقر",
                "new_pathologies": 0,
                "resolved_pathologies": 1,
                "progressed_pathologies": 0,
                "stable_pathologies": 2
            }
        }


class ClinicalReportResult(BaseModel):
    """
    Clinical report data
    بيانات التقرير السريري
    """
    title: str = Field(..., description="Report title")
    summary: str = Field(..., description="Results summary")
    risk_level: RiskLevel = Field(..., description="Overall risk level")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Overall confidence")
    findings: List[ComprehensivePathologyResult] = Field(..., description="Detailed findings")
    requires_urgent_attention: bool = Field(..., description="Urgent attention flag")
    generated_at: str = Field(..., description="Report generation timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "title": "تقرير تحليل الأشعة السينية للأسنان",
                "summary": "تم اكتشاف 2 حالة مرضية، منها 1 تتطلب عناية عاجلة",
                "risk_level": "medium",
                "confidence_score": 0.88,
                "findings": [],
                "requires_urgent_attention": True,
                "generated_at": "2024-01-15T10:30:00"
            }
        }


class EnhancedAnalysisResponse(BaseModel):
    """
    Enhanced comprehensive analysis response
    استجابة التحليل الشامل المحسنة
    """
    analysis_id: str = Field(..., description="Unique analysis identifier")
    patient_id: str = Field(..., description="Patient identifier")
    status: str = Field(..., description="Analysis status")
    detected_pathologies: List[ComprehensivePathologyResult] = Field(..., description="Detected pathologies")
    overall_severity: float = Field(..., ge=0.0, le=1.0, description="Overall severity score")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Overall confidence score")
    risk_level: RiskLevel = Field(..., description="Overall risk assessment")
    requires_urgent_attention: bool = Field(..., description="Urgent attention required")
    visualizations: Optional[VisualizationData] = Field(None, description="Visualization data")
    temporal_analysis: Optional[TemporalAnalysisResult] = Field(None, description="Temporal comparison")
    clinical_report: ClinicalReportResult = Field(..., description="Clinical report")
    language: str = Field(..., description="Response language")
    analysis_timestamp: str = Field(..., description="Analysis timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
                "patient_id": "patient-123",
                "status": "completed",
                "detected_pathologies": [],
                "overall_severity": 0.65,
                "confidence_score": 0.88,
                "risk_level": "medium",
                "requires_urgent_attention": True,
                "language": "ar",
                "analysis_timestamp": "2024-01-15T10:30:00"
            }
        }


class BatchAnalysisRequest(BaseModel):
    """
    Batch analysis request
    طلب التحليل المجمع
    """
    patient_id: str = Field(..., description="Patient identifier")
    language: str = Field(default="ar", description="Response language")
    include_heatmaps: bool = Field(default=False, description="Include heatmap visualizations")


class BatchAnalysisResult(BaseModel):
    """
    Individual batch analysis result
    نتيجة التحليل المجمع الفردية
    """
    filename: str = Field(..., description="Original filename")
    analysis_id: Optional[str] = Field(None, description="Analysis identifier if successful")
    status: str = Field(..., description="Analysis status")
    pathologies_count: Optional[int] = Field(None, description="Number of pathologies detected")
    risk_level: Optional[RiskLevel] = Field(None, description="Risk level")
    urgent: Optional[bool] = Field(None, description="Urgent attention required")
    error: Optional[str] = Field(None, description="Error message if failed")


class BatchAnalysisResponse(BaseModel):
    """
    Batch analysis response
    استجابة التحليل المجمع
    """
    batch_id: str = Field(..., description="Batch identifier")
    total_files: int = Field(..., description="Total files processed")
    successful: int = Field(..., description="Successfully processed files")
    failed: int = Field(..., description="Failed files")
    results: List[BatchAnalysisResult] = Field(..., description="Individual results")


class PathologyInfo(BaseModel):
    """
    Pathology information with translations
    معلومات الأمراض مع الترجمات
    """
    id: str = Field(..., description="Pathology identifier")
    name: str = Field(..., description="Pathology name in requested language")
    severity: SeverityLevel = Field(..., description="Default severity level")
    urgent: bool = Field(..., description="Typically requires urgent attention")


class ModelInfo(BaseModel):
    """
    AI model information
    معلومات نموذج الذكاء الاصطناعي
    """
    model_type: str = Field(..., description="Model architecture type")
    supported_pathologies: int = Field(..., description="Number of supported pathologies")
    supported_languages: List[str] = Field(..., description="Supported languages")
    features: List[str] = Field(..., description="Model features")
    device: str = Field(..., description="Computation device")
    model_loaded: bool = Field(..., description="Model loading status")


class AnalysisReportRequest(BaseModel):
    """
    Analysis report generation request
    طلب إنشاء تقرير التحليل
    """
    analysis_id: str = Field(..., description="Analysis identifier")
    language: str = Field(default="ar", description="Report language")
    format: str = Field(default="json", description="Report format (json, pdf, html)")
    include_images: bool = Field(default=True, description="Include images in report")
    include_recommendations: bool = Field(default=True, description="Include clinical recommendations")


class AnalysisReportResponse(BaseModel):
    """
    Analysis report response
    استجابة تقرير التحليل
    """
    report_id: str = Field(..., description="Report identifier")
    format: str = Field(..., description="Report format")
    content: Optional[str] = Field(None, description="Report content (for HTML/JSON)")
    download_url: Optional[str] = Field(None, description="Download URL (for PDF)")
    generated_at: str = Field(..., description="Generation timestamp")
    language: str = Field(..., description="Report language")


class QualityMetrics(BaseModel):
    """
    Image quality assessment metrics
    مقاييس تقييم جودة الصورة
    """
    image_quality_score: float = Field(..., ge=0.0, le=1.0, description="Overall image quality")
    contrast_score: float = Field(..., ge=0.0, le=1.0, description="Image contrast quality")
    sharpness_score: float = Field(..., ge=0.0, le=1.0, description="Image sharpness")
    noise_level: float = Field(..., ge=0.0, le=1.0, description="Image noise level")
    positioning_score: float = Field(..., ge=0.0, le=1.0, description="X-ray positioning quality")
    recommendations: List[str] = Field(..., description="Image quality improvement recommendations")


class EnhancedAnalysisWithQuality(EnhancedAnalysisResponse):
    """
    Enhanced analysis response with quality metrics
    استجابة التحليل المحسنة مع مقاييس الجودة
    """
    quality_metrics: QualityMetrics = Field(..., description="Image quality assessment")
    processing_time: float = Field(..., description="Analysis processing time in seconds")
    model_version: str = Field(..., description="AI model version used")
    
    class Config:
        schema_extra = {
            "example": {
                "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
                "patient_id": "patient-123",
                "status": "completed",
                "detected_pathologies": [],
                "overall_severity": 0.65,
                "confidence_score": 0.88,
                "risk_level": "medium",
                "requires_urgent_attention": True,
                "language": "ar",
                "analysis_timestamp": "2024-01-15T10:30:00",
                "quality_metrics": {
                    "image_quality_score": 0.85,
                    "contrast_score": 0.90,
                    "sharpness_score": 0.80,
                    "noise_level": 0.15,
                    "positioning_score": 0.88,
                    "recommendations": ["Improve positioning for better clarity"]
                },
                "processing_time": 2.5,
                "model_version": "v2.1.0"
            }
        }
