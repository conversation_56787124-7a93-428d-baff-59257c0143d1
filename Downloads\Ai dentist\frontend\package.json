{"name": "dental-ai-frontend", "version": "1.0.0", "description": "AI Dental Assistant Frontend - <PERSON><PERSON><PERSON><PERSON><PERSON> طبيب الأسنان الذكي", "private": true, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "@reduxjs/toolkit": "^1.9.7", "axios": "^1.6.2", "dayjs": "^1.11.10", "i18next": "^23.7.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.30.0", "react-i18next": "^13.5.0", "react-redux": "^8.1.3", "react-router-dom": "^6.20.1", "react-scripts": "^3.0.1", "recharts": "^2.8.0", "redux-persist": "^6.0.0", "stylis": "^4.3.0", "stylis-plugin-rtl": "^2.1.1", "web-vitals": "^3.5.0"}, "scripts": {"start": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "build": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@types/stylis": "^4.2.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "typescript": "^4.9.5"}}