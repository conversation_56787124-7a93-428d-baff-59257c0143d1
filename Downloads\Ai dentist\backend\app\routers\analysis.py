"""
X-ray analysis router for AI Dental Assistant
موجه تحليل الأشعة السينية لمساعد طبيب الأسنان الذكي
"""

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import os
import shutil
from pathlib import Path

from backend.database.connection import get_db
from backend.database.models import User, Patient, XrayAnalysis
from backend.app.routers.auth import get_current_active_user
from backend.app.schemas.analysis import XrayAnalysisCreate, XrayAnalysisResponse, XrayAnalysisUpdate
from backend.app.utils.security import generate_secure_filename, validate_file_type, check_file_size
from backend.app.utils.audit import log_user_action, AuditActions, AuditResourceTypes
from backend.app.config import settings
# from ...ai_models.inference.dental_model import dental_analyzer
# Temporary mock for development
class MockDentalAnalyzer:
    def analyze_xray(self, image_path, language="ar"):
        return {
            "predictions": [
                {
                    "class": "caries",
                    "name": "تسوس" if language == "ar" else "carie",
                    "confidence": 0.85,
                    "severity": "medium",
                    "severity_localized": "متوسط" if language == "ar" else "moyen",
                    "recommendations": ["حشو الأسنان"] if language == "ar" else ["Obturation dentaire"],
                    "urgent": False
                }
            ],
            "metadata": {"format": "standard"},
            "heatmap_path": None,
            "processing_time": 1.5,
            "model_version": "1.0.0"
        }

dental_analyzer = MockDentalAnalyzer()

router = APIRouter()


@router.post("/upload-xray", response_model=XrayAnalysisResponse)
async def upload_and_analyze_xray(
    patient_id: int = Form(...),
    file: UploadFile = File(...),
    language: str = Form("ar"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Upload X-ray image and perform AI analysis
    تحميل صورة الأشعة السينية وإجراء التحليل بالذكاء الاصطناعي
    """
    # Validate patient access
    patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Patient not found",
                "message_ar": "المريض غير موجود"
            }
        )

    # Check if user can access this patient
    if current_user.role != "admin" and patient.dentist_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Access denied to patient data",
                "message_ar": "تم رفض الوصول إلى بيانات المريض"
            }
        )

    # Validate file type
    if not validate_file_type(file.filename, settings.ALLOWED_IMAGE_EXTENSIONS):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": f"Invalid file type. Allowed: {settings.ALLOWED_IMAGE_EXTENSIONS}",
                "message_ar": f"نوع ملف غير صالح. المسموح: {settings.ALLOWED_IMAGE_EXTENSIONS}"
            }
        )

    # Check file size
    file_content = await file.read()
    if not check_file_size(len(file_content)):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes",
                "message_ar": f"الملف كبير جداً. الحد الأقصى: {settings.MAX_FILE_SIZE} بايت"
            }
        )

    try:
        # Generate secure filename
        secure_filename = generate_secure_filename(file.filename, current_user.id)

        # Create upload directory if it doesn't exist
        upload_dir = Path(settings.UPLOAD_DIR) / "xrays" / str(patient_id)
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Save file
        file_path = upload_dir / secure_filename
        with open(file_path, "wb") as buffer:
            buffer.write(file_content)

        # Perform AI analysis
        analysis_results = dental_analyzer.analyze_xray(str(file_path), language)

        # Create database record
        xray_analysis = XrayAnalysis(
            patient_id=patient_id,
            dentist_id=current_user.id,
            image_path=str(file_path),
            image_format=file.filename.split('.')[-1].upper(),
            image_size=len(file_content),
            image_metadata=analysis_results.get("metadata", {}),
            ai_predictions=analysis_results.get("predictions", []),
            confidence_scores={},
            heatmap_path=analysis_results.get("heatmap_path"),
            detected_pathologies=analysis_results.get("predictions", []),
            model_version=analysis_results.get("model_version", "1.0.0"),
            processing_time=analysis_results.get("processing_time", 0)
        )

        db.add(xray_analysis)
        db.commit()
        db.refresh(xray_analysis)

        # Log the action
        await log_user_action(
            db=db,
            user_id=current_user.id,
            action=AuditActions.XRAY_UPLOAD,
            resource_type=AuditResourceTypes.XRAY,
            resource_id=str(xray_analysis.id),
            details={
                "patient_id": patient_id,
                "filename": file.filename,
                "file_size": len(file_content),
                "pathologies_detected": len(analysis_results.get("predictions", []))
            }
        )

        return XrayAnalysisResponse.from_orm(xray_analysis)

    except Exception as e:
        # Clean up file if analysis failed
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": f"Analysis failed: {str(e)}",
                "message_ar": f"فشل التحليل: {str(e)}"
            }
        )


@router.get("/patient/{patient_id}/analyses", response_model=List[XrayAnalysisResponse])
async def get_patient_analyses(
    patient_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all X-ray analyses for a patient
    الحصول على جميع تحليلات الأشعة السينية للمريض
    """
    # Validate patient access
    patient = db.query(Patient).filter(Patient.id == patient_id).first()
    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Patient not found",
                "message_ar": "المريض غير موجود"
            }
        )

    # Check access permissions
    if current_user.role != "admin" and patient.dentist_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Access denied to patient data",
                "message_ar": "تم رفض الوصول إلى بيانات المريض"
            }
        )

    # Get analyses
    analyses = db.query(XrayAnalysis).filter(
        XrayAnalysis.patient_id == patient_id
    ).order_by(XrayAnalysis.created_at.desc()).offset(skip).limit(limit).all()

    # Log the action
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action=AuditActions.XRAY_VIEW,
        resource_type=AuditResourceTypes.PATIENT,
        resource_id=str(patient_id),
        details={"analyses_count": len(analyses)}
    )

    return [XrayAnalysisResponse.from_orm(analysis) for analysis in analyses]


@router.get("/analysis/{analysis_id}", response_model=XrayAnalysisResponse)
async def get_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get specific X-ray analysis
    الحصول على تحليل أشعة سينية محدد
    """
    analysis = db.query(XrayAnalysis).filter(XrayAnalysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Analysis not found",
                "message_ar": "التحليل غير موجود"
            }
        )

    # Check access permissions
    if current_user.role != "admin" and analysis.dentist_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Access denied to analysis data",
                "message_ar": "تم رفض الوصول إلى بيانات التحليل"
            }
        )

    # Log the action
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action=AuditActions.XRAY_VIEW,
        resource_type=AuditResourceTypes.XRAY,
        resource_id=str(analysis_id)
    )

    return XrayAnalysisResponse.from_orm(analysis)


@router.put("/analysis/{analysis_id}/review", response_model=XrayAnalysisResponse)
async def update_analysis_review(
    analysis_id: int,
    review_data: XrayAnalysisUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update dentist's review of AI analysis
    تحديث مراجعة طبيب الأسنان للتحليل الذكي
    """
    analysis = db.query(XrayAnalysis).filter(XrayAnalysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Analysis not found",
                "message_ar": "التحليل غير موجود"
            }
        )

    # Check permissions (only dentist can review)
    if current_user.role not in ["admin", "dentist"] or analysis.dentist_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Only the treating dentist can review this analysis",
                "message_ar": "يمكن فقط لطبيب الأسنان المعالج مراجعة هذا التحليل"
            }
        )

    # Update review fields
    update_data = review_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(analysis, field):
            setattr(analysis, field, value)

    # Mark as reviewed
    analysis.is_reviewed = True
    analysis.review_date = datetime.utcnow()

    db.commit()
    db.refresh(analysis)

    # Log the action
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action=AuditActions.DIAGNOSIS_UPDATE,
        resource_type=AuditResourceTypes.XRAY,
        resource_id=str(analysis_id),
        details={
            "reviewed": True,
            "updated_fields": list(update_data.keys())
        }
    )

    return XrayAnalysisResponse.from_orm(analysis)


@router.delete("/analysis/{analysis_id}")
async def delete_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete X-ray analysis (admin or dentist only)
    حذف تحليل الأشعة السينية (المدير أو طبيب الأسنان فقط)
    """
    analysis = db.query(XrayAnalysis).filter(XrayAnalysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Analysis not found",
                "message_ar": "التحليل غير موجود"
            }
        )

    # Check permissions
    if current_user.role not in ["admin", "dentist"] or analysis.dentist_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Insufficient permissions to delete analysis",
                "message_ar": "صلاحيات غير كافية لحذف التحليل"
            }
        )

    # Delete associated files
    try:
        if analysis.image_path and os.path.exists(analysis.image_path):
            os.remove(analysis.image_path)

        if analysis.heatmap_path and os.path.exists(analysis.heatmap_path):
            os.remove(analysis.heatmap_path)
    except Exception as e:
        print(f"Error deleting files: {e}")

    # Delete database record
    db.delete(analysis)
    db.commit()

    # Log the action
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action=AuditActions.XRAY_DELETE,
        resource_type=AuditResourceTypes.XRAY,
        resource_id=str(analysis_id),
        details={"patient_id": analysis.patient_id}
    )

    return {
        "message": "Analysis deleted successfully",
        "message_ar": "تم حذف التحليل بنجاح"
    }


@router.post("/reanalyze/{analysis_id}", response_model=XrayAnalysisResponse)
async def reanalyze_xray(
    analysis_id: int,
    language: str = "ar",
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Re-run AI analysis on existing X-ray
    إعادة تشغيل التحليل الذكي على الأشعة السينية الموجودة
    """
    analysis = db.query(XrayAnalysis).filter(XrayAnalysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Analysis not found",
                "message_ar": "التحليل غير موجود"
            }
        )

    # Check permissions
    if current_user.role != "admin" and analysis.dentist_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Access denied to analysis data",
                "message_ar": "تم رفض الوصول إلى بيانات التحليل"
            }
        )

    # Check if image file exists
    if not os.path.exists(analysis.image_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Original image file not found",
                "message_ar": "ملف الصورة الأصلي غير موجود"
            }
        )

    try:
        # Re-run AI analysis
        analysis_results = dental_analyzer.analyze_xray(analysis.image_path, language)

        # Update analysis record
        analysis.ai_predictions = analysis_results.get("predictions", [])
        analysis.detected_pathologies = analysis_results.get("predictions", [])
        analysis.heatmap_path = analysis_results.get("heatmap_path")
        analysis.model_version = analysis_results.get("model_version", "1.0.0")
        analysis.processing_time = analysis_results.get("processing_time", 0)
        analysis.is_reviewed = False  # Reset review status
        analysis.review_date = None

        db.commit()
        db.refresh(analysis)

        # Log the action
        await log_user_action(
            db=db,
            user_id=current_user.id,
            action=AuditActions.XRAY_ANALYZE,
            resource_type=AuditResourceTypes.XRAY,
            resource_id=str(analysis_id),
            details={
                "reanalysis": True,
                "pathologies_detected": len(analysis_results.get("predictions", []))
            }
        )

        return XrayAnalysisResponse.from_orm(analysis)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": f"Re-analysis failed: {str(e)}",
                "message_ar": f"فشل إعادة التحليل: {str(e)}"
            }
        )
