# 🦷 AI Dental Assistant - م<PERSON><PERSON><PERSON><PERSON> طبيب الأسنان الذكي

## Overview / نظرة عامة

A comprehensive AI-powered dental assistant application specifically designed for the Tunisian market. This application provides intelligent X-ray analysis, patient management, and clinical decision support while ensuring full compliance with Tunisian regulations (INPDP).

تطبيق مساعد طبيب أسنان ذكي شامل مصمم خصيصاً للسوق التونسي. يوفر التطبيق تحليل ذكي للأشعة السينية وإدارة المرضى ودعم القرارات السريرية مع ضمان الامتثال الكامل للقوانين التونسية.

## 🚀 Key Features / الميزات الرئيسية

### 🔬 AI-Powered X-ray Analysis / تحليل الأشعة بالذكاء الاصطناعي
- Multi-pathology detection (caries, infections, fractures, cysts, tumors)
- Heatmap visualization for problematic areas
- Temporal comparison for treatment progress tracking
- Support for DICOM, JPG, PNG formats
- Offline-first architecture with cloud synchronization

### 👥 Patient Management / إدارة المرضى
- INPDP-compliant patient records
- Intelligent treatment history
- Automated follow-up alerts
- Consent management system
- Integration with local systems (Medicalplus, Denticab)

### 🌐 Bilingual Interface / واجهة ثنائية اللغة
- Complete Arabic and French support
- Tunisian dialect adaptation
- Medical terminology in both languages
- Cultural adaptation for local practices

### 📱 Telemedicine / الطب عن بُعد
- Secure video consultations
- Remote patient monitoring
- Mobile-optimized for Tunisian networks
- CNAM integration ready

### 🔒 Security & Compliance / الأمان والامتثال
- End-to-end encryption
- INPDP compliance
- Audit trails
- Data anonymization for research

## 🏗️ Architecture / البنية المعمارية

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Frontend      │     │    Backend      │     │   AI Services   │
│   React.js      │────▶│   FastAPI       │────▶│   PyTorch       │
│   Arabic/French │     │   PostgreSQL    │     │   CNN Models    │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## 📁 Project Structure / هيكل المشروع

```
dental-ai-app/
├── backend/                 # Backend API services
│   ├── app/                # FastAPI application
│   ├── models/             # AI models and inference
│   ├── database/           # Database models and migrations
│   └── requirements.txt    # Python dependencies
├── frontend/               # React frontend application
│   ├── src/               # Source code
│   ├── public/            # Static assets
│   └── package.json       # Node.js dependencies
├── ai-models/             # AI model training and inference
│   ├── training/          # Model training scripts
│   ├── inference/         # Inference pipeline
│   └── data/             # Training data and preprocessing
├── docker/               # Docker configuration
├── docs/                 # Documentation
└── tests/               # Test suites
```

## 🛠️ Technology Stack / المكدس التقني

### Backend
- **FastAPI** - Modern Python web framework
- **PostgreSQL** - Primary database
- **Redis** - Caching and session management
- **SQLAlchemy** - ORM
- **Alembic** - Database migrations

### Frontend
- **React.js** - User interface framework
- **TypeScript** - Type-safe JavaScript
- **Material-UI** - Component library
- **React-i18next** - Internationalization
- **PWA** - Progressive Web App capabilities

### AI/ML
- **PyTorch** - Deep learning framework
- **OpenCV** - Image processing
- **DICOM** - Medical imaging support
- **Grad-CAM** - Visualization techniques

### DevOps
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration
- **GitHub Actions** - CI/CD pipeline

## 🚀 Quick Start / البدء السريع

### Prerequisites / المتطلبات المسبقة
- Python 3.9+
- Node.js 16+
- Docker & Docker Compose
- PostgreSQL 13+

### Installation / التثبيت

1. **Clone the repository / استنساخ المستودع**
```bash
git clone https://github.com/your-org/dental-ai-app.git
cd dental-ai-app
```

2. **Setup Backend / إعداد الخادم الخلفي**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **Setup Frontend / إعداد الواجهة الأمامية**
```bash
cd frontend
npm install
```

4. **Start with Docker / البدء باستخدام Docker**
```bash
docker-compose up -d
```

5. **Access the application / الوصول إلى التطبيق**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## 📖 Documentation / التوثيق

- [API Documentation](docs/api.md)
- [User Guide - English](docs/user-guide-en.md)
- [دليل المستخدم - العربية](docs/user-guide-ar.md)
- [Developer Guide](docs/developer-guide.md)
- [Deployment Guide](docs/deployment.md)

## 🧪 Testing / الاختبار

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test

# Integration tests
docker-compose -f docker-compose.test.yml up
```

## 🤝 Contributing / المساهمة

Please read our [Contributing Guidelines](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License / الترخيص

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🏥 Clinical Validation / التحقق السريري

This application is designed for clinical validation with:
- Faculty of Dental Medicine of Monastir
- Tunisian Association of Dental Surgeons (ATMD)
- Partner clinics across Tunisia

## 📞 Support / الدعم

For technical support or questions:
- Email: <EMAIL>
- Phone: +216 XX XXX XXX
- Documentation: [docs.dental-ai.tn](https://docs.dental-ai.tn)

---

**Note**: This application is designed specifically for the Tunisian healthcare market and complies with local regulations including INPDP requirements.

**ملاحظة**: هذا التطبيق مصمم خصيصاً للسوق الصحي التونسي ويتوافق مع القوانين المحلية بما في ذلك متطلبات الهيئة الوطنية لحماية المعطيات الشخصية.
