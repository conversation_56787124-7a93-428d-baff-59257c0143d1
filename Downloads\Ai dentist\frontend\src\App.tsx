/**
 * Main App component for AI Dental Assistant
 * مكون التطبيق الرئيسي لمساعد طبيب الأسنان الذكي
 */

import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Box, CircularProgress, Alert } from '@mui/material';

import { RootState, AppDispatch } from './store/store';
import { checkAuthStatus } from './store/slices/authSlice';
import { useI18n } from './i18n/useI18n';

// Layout Components
import MainLayout from './components/layout/MainLayout';
import AuthLayout from './components/layout/AuthLayout';

// Page Components
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import PatientsPage from './pages/patients/PatientsPage';
import PatientDetailPage from './pages/patients/PatientDetailPage';
import AnalysisPage from './pages/analysis/AnalysisPage';
import ReportsPage from './pages/reports/ReportsPage';
import TelemedicinePage from './pages/telemedicine/TelemedicinePage';
import SettingsPage from './pages/settings/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';

// Protected Route Component
import ProtectedRoute from './components/auth/ProtectedRoute';

// Error Boundary
import ErrorBoundary from './components/common/ErrorBoundary';

function App() {
  const dispatch = useDispatch<AppDispatch>();
  const { isAuthenticated, isLoading, error } = useSelector((state: RootState) => state.auth);
  const { t, changeLanguage, currentLanguage } = useI18n();

  useEffect(() => {
    // Check authentication status on app load
    dispatch(checkAuthStatus());
  }, [dispatch]);

  useEffect(() => {
    // Update document direction based on language
    document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLanguage;
  }, [currentLanguage]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        bgcolor="background.default"
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  // Show error if authentication check failed
  if (error) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        bgcolor="background.default"
        p={3}
      >
        <Alert severity="error" sx={{ maxWidth: 400 }}>
          {t('errors.authenticationFailed')}
        </Alert>
      </Box>
    );
  }

  return (
    <ErrorBoundary>
      <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
        <Routes>
          {/* Public Routes */}
          <Route
            path="/auth/*"
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <AuthLayout>
                  <Routes>
                    <Route path="login" element={<LoginPage />} />
                    <Route path="register" element={<RegisterPage />} />
                    <Route path="*" element={<Navigate to="login" replace />} />
                  </Routes>
                </AuthLayout>
              )
            }
          />

          {/* Protected Routes */}
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <Routes>
                    {/* Dashboard */}
                    <Route path="dashboard" element={<DashboardPage />} />

                    {/* Patients */}
                    <Route path="patients" element={<PatientsPage />} />
                    <Route path="patients/:patientId" element={<PatientDetailPage />} />

                    {/* Analysis */}
                    <Route path="analysis" element={<AnalysisPage />} />
                    <Route path="analysis/:analysisId" element={<AnalysisPage />} />

                    {/* Reports */}
                    <Route path="reports" element={<ReportsPage />} />

                    {/* Telemedicine */}
                    <Route path="telemedicine" element={<TelemedicinePage />} />

                    {/* Settings */}
                    <Route path="settings" element={<SettingsPage />} />

                    {/* Default redirect */}
                    <Route path="/" element={<Navigate to="/dashboard" replace />} />

                    {/* 404 */}
                    <Route path="*" element={<NotFoundPage />} />
                  </Routes>
                </MainLayout>
              </ProtectedRoute>
            }
          />

          {/* Root redirect */}
          <Route
            path="/"
            element={
              <Navigate
                to={isAuthenticated ? "/dashboard" : "/auth/login"}
                replace
              />
            }
          />
        </Routes>
      </Box>
    </ErrorBoundary>
  );
}

export default App;
