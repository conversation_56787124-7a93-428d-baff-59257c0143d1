/**
 * Analysis slice for Redux store
 * شريحة التحليل لمتجر Redux
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { analysisAPI } from '../../services/api';

// Types
interface PathologyPrediction {
  class_name: string;
  name: string;
  confidence: number;
  severity: string;
  severity_localized: string;
  recommendations: string[];
  urgent: boolean;
}

interface XrayAnalysis {
  id: number;
  uuid: string;
  patient_id: number;
  dentist_id: number;
  image_path: string;
  image_format: string;
  image_size: number;
  image_metadata: any;
  ai_predictions: PathologyPrediction[];
  confidence_scores: any;
  heatmap_path?: string;
  detected_pathologies: PathologyPrediction[];
  dentist_review?: string;
  dentist_diagnosis?: string;
  is_reviewed: boolean;
  review_date?: string;
  recommended_treatment?: string;
  treatment_priority: string;
  follow_up_required: boolean;
  follow_up_date?: string;
  model_version: string;
  processing_time: number;
  created_at: string;
  updated_at?: string;
}

interface AnalysisState {
  analyses: XrayAnalysis[];
  currentAnalysis: XrayAnalysis | null;
  isLoading: boolean;
  isUploading: boolean;
  isAnalyzing: boolean;
  error: string | null;
  uploadProgress: number;
  analysisProgress: number;
  statistics: {
    total_analyses: number;
    pathologies_detected: Record<string, number>;
    severity_distribution: Record<string, number>;
    average_processing_time: number;
    reviewed_percentage: number;
    urgent_cases: number;
  } | null;
}

interface UploadAnalysisData {
  patient_id: number;
  file: File;
  language?: string;
}

interface UpdateAnalysisData {
  analysis_id: number;
  dentist_review?: string;
  dentist_diagnosis?: string;
  recommended_treatment?: string;
  treatment_priority?: string;
  follow_up_required?: boolean;
  follow_up_date?: string;
}

// Initial state
const initialState: AnalysisState = {
  analyses: [],
  currentAnalysis: null,
  isLoading: false,
  isUploading: false,
  isAnalyzing: false,
  error: null,
  uploadProgress: 0,
  analysisProgress: 0,
  statistics: null,
};

// Async thunks
export const uploadAndAnalyzeXray = createAsyncThunk(
  'analysis/uploadAndAnalyze',
  async (data: UploadAnalysisData, { rejectWithValue, dispatch }) => {
    try {
      dispatch(setUploadProgress(0));
      dispatch(setAnalysisProgress(0));
      
      const formData = new FormData();
      formData.append('patient_id', data.patient_id.toString());
      formData.append('file', data.file);
      formData.append('language', data.language || 'ar');

      const response = await analysisAPI.uploadAndAnalyze(formData, {
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          dispatch(setUploadProgress(progress));
        },
      });

      dispatch(setAnalysisProgress(100));
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Analysis failed');
    }
  }
);

export const fetchPatientAnalyses = createAsyncThunk(
  'analysis/fetchPatientAnalyses',
  async (patientId: number, { rejectWithValue }) => {
    try {
      const response = await analysisAPI.getPatientAnalyses(patientId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch analyses');
    }
  }
);

export const fetchAnalysisById = createAsyncThunk(
  'analysis/fetchAnalysisById',
  async (analysisId: number, { rejectWithValue }) => {
    try {
      const response = await analysisAPI.getAnalysisById(analysisId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch analysis');
    }
  }
);

export const updateAnalysisReview = createAsyncThunk(
  'analysis/updateReview',
  async ({ analysis_id, ...reviewData }: UpdateAnalysisData, { rejectWithValue }) => {
    try {
      const response = await analysisAPI.updateAnalysisReview(analysis_id, reviewData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to update review');
    }
  }
);

export const deleteAnalysis = createAsyncThunk(
  'analysis/deleteAnalysis',
  async (analysisId: number, { rejectWithValue }) => {
    try {
      await analysisAPI.deleteAnalysis(analysisId);
      return analysisId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to delete analysis');
    }
  }
);

export const reanalyzeXray = createAsyncThunk(
  'analysis/reanalyze',
  async ({ analysisId, language = 'ar' }: { analysisId: number; language?: string }, { rejectWithValue }) => {
    try {
      const response = await analysisAPI.reanalyzeXray(analysisId, language);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Re-analysis failed');
    }
  }
);

export const fetchAnalysisStatistics = createAsyncThunk(
  'analysis/fetchStatistics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await analysisAPI.getStatistics();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch statistics');
    }
  }
);

// Analysis slice
const analysisSlice = createSlice({
  name: 'analysis',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentAnalysis: (state, action: PayloadAction<XrayAnalysis | null>) => {
      state.currentAnalysis = action.payload;
    },
    setUploadProgress: (state, action: PayloadAction<number>) => {
      state.uploadProgress = action.payload;
    },
    setAnalysisProgress: (state, action: PayloadAction<number>) => {
      state.analysisProgress = action.payload;
    },
    clearAnalyses: (state) => {
      state.analyses = [];
      state.currentAnalysis = null;
      state.statistics = null;
    },
    resetProgress: (state) => {
      state.uploadProgress = 0;
      state.analysisProgress = 0;
    },
  },
  extraReducers: (builder) => {
    // Upload and analyze
    builder
      .addCase(uploadAndAnalyzeXray.pending, (state) => {
        state.isUploading = true;
        state.isAnalyzing = true;
        state.error = null;
      })
      .addCase(uploadAndAnalyzeXray.fulfilled, (state, action) => {
        state.isUploading = false;
        state.isAnalyzing = false;
        state.analyses.unshift(action.payload);
        state.currentAnalysis = action.payload;
        state.error = null;
        state.uploadProgress = 100;
        state.analysisProgress = 100;
      })
      .addCase(uploadAndAnalyzeXray.rejected, (state, action) => {
        state.isUploading = false;
        state.isAnalyzing = false;
        state.error = action.payload as string;
        state.uploadProgress = 0;
        state.analysisProgress = 0;
      });

    // Fetch patient analyses
    builder
      .addCase(fetchPatientAnalyses.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPatientAnalyses.fulfilled, (state, action) => {
        state.isLoading = false;
        state.analyses = action.payload;
        state.error = null;
      })
      .addCase(fetchPatientAnalyses.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch analysis by ID
    builder
      .addCase(fetchAnalysisById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAnalysisById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentAnalysis = action.payload;
        state.error = null;
      })
      .addCase(fetchAnalysisById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update analysis review
    builder
      .addCase(updateAnalysisReview.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateAnalysisReview.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.analyses.findIndex(a => a.id === action.payload.id);
        if (index !== -1) {
          state.analyses[index] = action.payload;
        }
        if (state.currentAnalysis?.id === action.payload.id) {
          state.currentAnalysis = action.payload;
        }
        state.error = null;
      })
      .addCase(updateAnalysisReview.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Delete analysis
    builder
      .addCase(deleteAnalysis.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteAnalysis.fulfilled, (state, action) => {
        state.isLoading = false;
        state.analyses = state.analyses.filter(a => a.id !== action.payload);
        if (state.currentAnalysis?.id === action.payload) {
          state.currentAnalysis = null;
        }
        state.error = null;
      })
      .addCase(deleteAnalysis.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Reanalyze
    builder
      .addCase(reanalyzeXray.pending, (state) => {
        state.isAnalyzing = true;
        state.error = null;
      })
      .addCase(reanalyzeXray.fulfilled, (state, action) => {
        state.isAnalyzing = false;
        const index = state.analyses.findIndex(a => a.id === action.payload.id);
        if (index !== -1) {
          state.analyses[index] = action.payload;
        }
        if (state.currentAnalysis?.id === action.payload.id) {
          state.currentAnalysis = action.payload;
        }
        state.error = null;
      })
      .addCase(reanalyzeXray.rejected, (state, action) => {
        state.isAnalyzing = false;
        state.error = action.payload as string;
      });

    // Fetch statistics
    builder
      .addCase(fetchAnalysisStatistics.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAnalysisStatistics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.statistics = action.payload;
        state.error = null;
      })
      .addCase(fetchAnalysisStatistics.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setCurrentAnalysis,
  setUploadProgress,
  setAnalysisProgress,
  clearAnalyses,
  resetProgress,
} = analysisSlice.actions;

export default analysisSlice.reducer;
