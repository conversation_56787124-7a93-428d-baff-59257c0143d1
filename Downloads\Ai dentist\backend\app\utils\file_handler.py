"""
File handling utilities for AI Dental Assistant
أدوات معالجة الملفات لمساعد طبيب الأسنان الذكي
"""

import os
import uuid
import aiofiles
from pathlib import Path
from typing import Optional, List
from fastapi import UploadFile, HTTPException
import magic
import cv2
import numpy as np
from PIL import Image
import pydicom

from backend.app.config import settings


async def save_uploaded_file(file: UploadFile, subfolder: str = "uploads") -> str:
    """
    Save uploaded file to disk
    حفظ الملف المرفوع على القرص
    """
    # Create unique filename
    file_extension = Path(file.filename).suffix.lower()
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    
    # Create directory if it doesn't exist
    upload_dir = Path(settings.UPLOAD_DIR) / subfolder
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # Full file path
    file_path = upload_dir / unique_filename
    
    # Save file
    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)
    
    return str(file_path)


def validate_image_file(file: UploadFile) -> bool:
    """
    Validate uploaded image file
    التحقق من صحة ملف الصورة المرفوع
    """
    # Check file extension
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in settings.ALLOWED_IMAGE_EXTENSIONS:
        return False
    
    # Check file size
    if file.size > settings.MAX_FILE_SIZE:
        return False
    
    return True


def validate_dicom_file(file_path: str) -> bool:
    """
    Validate DICOM file format
    التحقق من صحة ملف DICOM
    """
    try:
        ds = pydicom.dcmread(file_path)
        return True
    except Exception:
        return False


def convert_dicom_to_image(dicom_path: str, output_path: str) -> bool:
    """
    Convert DICOM file to standard image format
    تحويل ملف DICOM إلى تنسيق صورة قياسي
    """
    try:
        # Read DICOM file
        ds = pydicom.dcmread(dicom_path)
        
        # Get pixel array
        pixel_array = ds.pixel_array
        
        # Normalize to 0-255 range
        pixel_array = ((pixel_array - pixel_array.min()) / 
                      (pixel_array.max() - pixel_array.min()) * 255).astype(np.uint8)
        
        # Save as PNG
        cv2.imwrite(output_path, pixel_array)
        return True
        
    except Exception as e:
        print(f"Error converting DICOM: {e}")
        return False


def get_image_info(image_path: str) -> dict:
    """
    Get image information and metadata
    الحصول على معلومات الصورة والبيانات الوصفية
    """
    try:
        # Open image
        with Image.open(image_path) as img:
            info = {
                "format": img.format,
                "mode": img.mode,
                "size": img.size,
                "width": img.width,
                "height": img.height,
                "file_size": os.path.getsize(image_path)
            }
            
            # Add EXIF data if available
            if hasattr(img, '_getexif') and img._getexif():
                info["exif"] = img._getexif()
            
            return info
            
    except Exception as e:
        return {"error": str(e)}


def resize_image(input_path: str, output_path: str, max_size: tuple = (1024, 1024)) -> bool:
    """
    Resize image while maintaining aspect ratio
    تغيير حجم الصورة مع الحفاظ على نسبة العرض إلى الارتفاع
    """
    try:
        with Image.open(input_path) as img:
            # Calculate new size maintaining aspect ratio
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Save resized image
            img.save(output_path, optimize=True, quality=85)
            return True
            
    except Exception as e:
        print(f"Error resizing image: {e}")
        return False


def create_thumbnail(input_path: str, output_path: str, size: tuple = (200, 200)) -> bool:
    """
    Create thumbnail image
    إنشاء صورة مصغرة
    """
    try:
        with Image.open(input_path) as img:
            # Create thumbnail
            img.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Save thumbnail
            img.save(output_path, optimize=True, quality=75)
            return True
            
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
        return False


def cleanup_old_files(directory: str, days_old: int = 30) -> int:
    """
    Clean up old files from directory
    تنظيف الملفات القديمة من المجلد
    """
    import time
    
    count = 0
    cutoff_time = time.time() - (days_old * 24 * 60 * 60)
    
    try:
        for file_path in Path(directory).rglob("*"):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                file_path.unlink()
                count += 1
                
    except Exception as e:
        print(f"Error cleaning up files: {e}")
    
    return count


def get_file_mime_type(file_path: str) -> str:
    """
    Get MIME type of file
    الحصول على نوع MIME للملف
    """
    try:
        mime = magic.Magic(mime=True)
        return mime.from_file(file_path)
    except Exception:
        # Fallback to extension-based detection
        extension_map = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.dcm': 'application/dicom',
            '.dicom': 'application/dicom'
        }
        ext = Path(file_path).suffix.lower()
        return extension_map.get(ext, 'application/octet-stream')


def secure_filename(filename: str) -> str:
    """
    Generate secure filename
    إنشاء اسم ملف آمن
    """
    # Remove path components
    filename = os.path.basename(filename)
    
    # Replace unsafe characters
    unsafe_chars = '<>:"/\\|?*'
    for char in unsafe_chars:
        filename = filename.replace(char, '_')
    
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename


class FileManager:
    """
    File management utility class
    فئة أداة إدارة الملفات
    """
    
    def __init__(self, base_dir: str = None):
        self.base_dir = Path(base_dir or settings.UPLOAD_DIR)
        self.base_dir.mkdir(parents=True, exist_ok=True)
    
    async def save_file(self, file: UploadFile, subfolder: str = "") -> str:
        """Save uploaded file"""
        if not validate_image_file(file):
            raise HTTPException(status_code=400, detail="Invalid file format or size")
        
        return await save_uploaded_file(file, subfolder)
    
    def delete_file(self, file_path: str) -> bool:
        """Delete file safely"""
        try:
            path = Path(file_path)
            if path.exists() and path.is_file():
                path.unlink()
                return True
        except Exception as e:
            print(f"Error deleting file: {e}")
        return False
    
    def get_file_info(self, file_path: str) -> dict:
        """Get comprehensive file information"""
        path = Path(file_path)
        
        if not path.exists():
            return {"error": "File not found"}
        
        info = {
            "name": path.name,
            "size": path.stat().st_size,
            "created": path.stat().st_ctime,
            "modified": path.stat().st_mtime,
            "mime_type": get_file_mime_type(str(path))
        }
        
        # Add image-specific info
        if info["mime_type"].startswith("image/"):
            info.update(get_image_info(str(path)))
        
        return info
    
    def list_files(self, subfolder: str = "", pattern: str = "*") -> List[dict]:
        """List files in directory"""
        directory = self.base_dir / subfolder
        files = []
        
        try:
            for file_path in directory.glob(pattern):
                if file_path.is_file():
                    files.append(self.get_file_info(str(file_path)))
        except Exception as e:
            print(f"Error listing files: {e}")
        
        return files
    
    def cleanup_old_files(self, days_old: int = 30) -> int:
        """Clean up old files"""
        return cleanup_old_files(str(self.base_dir), days_old)


# Global file manager instance
file_manager = FileManager()
