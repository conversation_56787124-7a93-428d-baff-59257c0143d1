/**
 * UI slice for Redux store
 * شريحة واجهة المستخدم لمتجر Redux
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Types
interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  persistent?: boolean;
}

interface Modal {
  id: string;
  type: string;
  props?: any;
  isOpen: boolean;
}

interface UIState {
  // Navigation
  sidebarOpen: boolean;
  currentPage: string;
  breadcrumbs: Array<{ label: string; path?: string }>;
  
  // Loading states
  globalLoading: boolean;
  loadingStates: Record<string, boolean>;
  
  // Notifications
  notifications: Notification[];
  
  // Modals
  modals: Modal[];
  
  // Theme
  darkMode: boolean;
  
  // Language and direction
  language: 'ar' | 'fr' | 'en';
  direction: 'rtl' | 'ltr';
  
  // Mobile
  isMobile: boolean;
  
  // Filters and search
  globalSearch: string;
  activeFilters: Record<string, any>;
  
  // View preferences
  viewMode: 'grid' | 'list' | 'table';
  itemsPerPage: number;
  
  // Errors
  globalError: string | null;
}

// Initial state
const initialState: UIState = {
  sidebarOpen: true,
  currentPage: 'dashboard',
  breadcrumbs: [],
  globalLoading: false,
  loadingStates: {},
  notifications: [],
  modals: [],
  darkMode: false,
  language: 'ar',
  direction: 'rtl',
  isMobile: false,
  globalSearch: '',
  activeFilters: {},
  viewMode: 'table',
  itemsPerPage: 20,
  globalError: null,
};

// UI slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Navigation
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
    },
    setBreadcrumbs: (state, action: PayloadAction<Array<{ label: string; path?: string }>>) => {
      state.breadcrumbs = action.payload;
    },
    
    // Loading states
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.globalLoading = action.payload;
    },
    setLoadingState: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      state.loadingStates[action.payload.key] = action.payload.loading;
    },
    clearLoadingState: (state, action: PayloadAction<string>) => {
      delete state.loadingStates[action.payload];
    },
    
    // Notifications
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // Modals
    openModal: (state, action: PayloadAction<{ type: string; props?: any }>) => {
      const modal: Modal = {
        id: Date.now().toString(),
        type: action.payload.type,
        props: action.payload.props,
        isOpen: true,
      };
      state.modals.push(modal);
    },
    closeModal: (state, action: PayloadAction<string>) => {
      const index = state.modals.findIndex(m => m.id === action.payload);
      if (index !== -1) {
        state.modals[index].isOpen = false;
      }
    },
    removeModal: (state, action: PayloadAction<string>) => {
      state.modals = state.modals.filter(m => m.id !== action.payload);
    },
    closeAllModals: (state) => {
      state.modals.forEach(modal => {
        modal.isOpen = false;
      });
    },
    
    // Theme
    setDarkMode: (state, action: PayloadAction<boolean>) => {
      state.darkMode = action.payload;
    },
    toggleDarkMode: (state) => {
      state.darkMode = !state.darkMode;
    },
    
    // Language and direction
    setLanguage: (state, action: PayloadAction<'ar' | 'fr' | 'en'>) => {
      state.language = action.payload;
      state.direction = action.payload === 'ar' ? 'rtl' : 'ltr';
    },
    setDirection: (state, action: PayloadAction<'rtl' | 'ltr'>) => {
      state.direction = action.payload;
    },
    
    // Mobile
    setIsMobile: (state, action: PayloadAction<boolean>) => {
      state.isMobile = action.payload;
    },
    
    // Search and filters
    setGlobalSearch: (state, action: PayloadAction<string>) => {
      state.globalSearch = action.payload;
    },
    setActiveFilter: (state, action: PayloadAction<{ key: string; value: any }>) => {
      state.activeFilters[action.payload.key] = action.payload.value;
    },
    removeActiveFilter: (state, action: PayloadAction<string>) => {
      delete state.activeFilters[action.payload];
    },
    clearActiveFilters: (state) => {
      state.activeFilters = {};
    },
    
    // View preferences
    setViewMode: (state, action: PayloadAction<'grid' | 'list' | 'table'>) => {
      state.viewMode = action.payload;
    },
    setItemsPerPage: (state, action: PayloadAction<number>) => {
      state.itemsPerPage = action.payload;
    },
    
    // Errors
    setGlobalError: (state, action: PayloadAction<string | null>) => {
      state.globalError = action.payload;
    },
    clearGlobalError: (state) => {
      state.globalError = null;
    },
  },
});

export const {
  // Navigation
  setSidebarOpen,
  toggleSidebar,
  setCurrentPage,
  setBreadcrumbs,
  
  // Loading states
  setGlobalLoading,
  setLoadingState,
  clearLoadingState,
  
  // Notifications
  addNotification,
  removeNotification,
  clearNotifications,
  
  // Modals
  openModal,
  closeModal,
  removeModal,
  closeAllModals,
  
  // Theme
  setDarkMode,
  toggleDarkMode,
  
  // Language and direction
  setLanguage,
  setDirection,
  
  // Mobile
  setIsMobile,
  
  // Search and filters
  setGlobalSearch,
  setActiveFilter,
  removeActiveFilter,
  clearActiveFilters,
  
  // View preferences
  setViewMode,
  setItemsPerPage,
  
  // Errors
  setGlobalError,
  clearGlobalError,
} = uiSlice.actions;

export default uiSlice.reducer;
