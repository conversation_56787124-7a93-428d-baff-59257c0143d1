/**
 * Settings Page component
 * صفحة الإعدادات
 */

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
} from '@mui/material';
import { useI18n } from '../../i18n/useI18n';

const SettingsPage: React.FC = () => {
  const { t } = useI18n();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        {t('settings.title')}
      </Typography>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('common.loading')}...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('settings.title')} - {t('common.loading')}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SettingsPage;
