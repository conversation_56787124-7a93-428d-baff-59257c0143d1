#!/usr/bin/env python3
"""
Intelligent Data Organization Agent for AI Dental Assistant
وكيل تنظيم البيانات الذكي لمساعد طبيب الأسنان الذكي

This agent intelligently explores, analyzes, and organizes dental X-ray data
from any folder structure within the 'sets' directory.
"""

import os
import shutil
import json
import random
import hashlib
from pathlib import Path
from typing import List, Dict, Tuple, Set
import cv2
import numpy as np
from datetime import datetime
import logging
from collections import defaultdict, Counter

class IntelligentDataAgent:
    """
    Intelligent agent for exploring and organizing dental data
    وكيل ذكي لاستكشاف وتنظيم بيانات الأسنان
    """
    
    def __init__(self, source_dir: str = "sets", target_dir: str = "ai-models/data"):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        
        # Setup logging
        self.setup_logging()
        
        # Image processing settings
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.dcm', '.dicom'}
        self.min_image_size = (50, 50)  # Minimum width, height
        self.max_file_size_mb = 100
        
        # Advanced pathology detection patterns
        self.pathology_patterns = {
            'caries': {
                'keywords': ['caries', 'cavity', 'decay', 'rot', 'تسوس', 'نخر', 'carie', 'cavité'],
                'folder_patterns': ['caries', 'decay', 'cavity', 'تسوس'],
                'confidence': 0.9
            },
            'infection': {
                'keywords': ['infection', 'abscess', 'pus', 'inflam', 'عدوى', 'التهاب', 'خراج', 'صديد', 'infection', 'abcès'],
                'folder_patterns': ['infection', 'abscess', 'عدوى', 'التهاب'],
                'confidence': 0.85
            },
            'fracture': {
                'keywords': ['fracture', 'break', 'crack', 'split', 'broken', 'كسر', 'شرخ', 'انكسار', 'تشقق', 'fracture', 'cassure'],
                'folder_patterns': ['fracture', 'break', 'crack', 'كسر'],
                'confidence': 0.9
            },
            'cyst': {
                'keywords': ['cyst', 'cystic', 'fluid', 'كيس', 'تكيس', 'kyste'],
                'folder_patterns': ['cyst', 'كيس'],
                'confidence': 0.8
            },
            'tumor': {
                'keywords': ['tumor', 'tumour', 'mass', 'growth', 'neoplasm', 'lesion', 'ورم', 'كتلة', 'نمو', 'tumeur', 'masse'],
                'folder_patterns': ['tumor', 'mass', 'ورم'],
                'confidence': 0.85
            },
            'normal': {
                'keywords': ['normal', 'healthy', 'clean', 'good', 'clear', 'طبيعي', 'سليم', 'صحي', 'نظيف', 'normal', 'sain'],
                'folder_patterns': ['normal', 'healthy', 'طبيعي'],
                'confidence': 0.7
            }
        }
        
        # Statistics tracking
        self.stats = {
            'total_folders_explored': 0,
            'total_files_found': 0,
            'valid_images': 0,
            'invalid_images': 0,
            'duplicates_found': 0,
            'pathology_distribution': defaultdict(int),
            'folder_analysis': {}
        }
        
        # Create target structure
        self.create_directory_structure()
    
    def setup_logging(self):
        """Setup logging for the agent"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'data_agent.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_directory_structure(self):
        """Create the target directory structure"""
        directories = [
            self.target_dir / "train" / "images",
            self.target_dir / "train" / "masks",
            self.target_dir / "val" / "images",
            self.target_dir / "val" / "masks",
            self.target_dir / "test" / "images",
            self.target_dir / "test" / "masks",
            self.target_dir / "metadata"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"✅ Created directory structure in {self.target_dir}")
    
    def explore_directory_structure(self) -> Dict:
        """
        Intelligently explore the directory structure
        استكشاف هيكل الدليل بذكاء
        """
        self.logger.info(f"🔍 Starting intelligent exploration of {self.source_dir}")
        
        if not self.source_dir.exists():
            self.logger.error(f"❌ Source directory '{self.source_dir}' does not exist!")
            return {}
        
        exploration_map = {}
        
        for root, dirs, files in os.walk(self.source_dir):
            root_path = Path(root)
            relative_path = root_path.relative_to(self.source_dir)
            
            self.stats['total_folders_explored'] += 1
            
            # Analyze folder content
            folder_analysis = self.analyze_folder(root_path, files)
            exploration_map[str(relative_path)] = folder_analysis
            
            self.logger.info(f"📁 Explored: {relative_path} - Found {len(folder_analysis['images'])} images")
        
        self.stats['folder_analysis'] = exploration_map
        return exploration_map
    
    def analyze_folder(self, folder_path: Path, files: List[str]) -> Dict:
        """Analyze a single folder for content and patterns"""
        analysis = {
            'path': folder_path,
            'images': [],
            'non_images': [],
            'predicted_pathology': 'normal',
            'confidence': 0.0,
            'folder_keywords': [],
            'total_files': len(files)
        }
        
        # Extract keywords from folder path
        folder_text = str(folder_path).lower()
        detected_keywords = []
        
        for pathology, patterns in self.pathology_patterns.items():
            for keyword in patterns['keywords'] + patterns['folder_patterns']:
                if keyword.lower() in folder_text:
                    detected_keywords.append((pathology, keyword, patterns['confidence']))
        
        # Classify files
        for file in files:
            file_path = folder_path / file
            self.stats['total_files_found'] += 1
            
            if file_path.suffix.lower() in self.image_extensions:
                if self.validate_image(file_path):
                    analysis['images'].append({
                        'path': file_path,
                        'name': file,
                        'size': file_path.stat().st_size if file_path.exists() else 0,
                        'extension': file_path.suffix.lower()
                    })
                    self.stats['valid_images'] += 1
                else:
                    self.stats['invalid_images'] += 1
            else:
                analysis['non_images'].append(file)
        
        # Determine most likely pathology
        if detected_keywords:
            # Sort by confidence and take the highest
            detected_keywords.sort(key=lambda x: x[2], reverse=True)
            analysis['predicted_pathology'] = detected_keywords[0][0]
            analysis['confidence'] = detected_keywords[0][2]
            analysis['folder_keywords'] = [kw[1] for kw in detected_keywords]
        
        return analysis
    
    def validate_image(self, image_path: Path) -> bool:
        """Validate if image is usable for training"""
        try:
            # Check file size
            if image_path.stat().st_size > self.max_file_size_mb * 1024 * 1024:
                return False
            
            if image_path.suffix.lower() in ['.dcm', '.dicom']:
                # For DICOM files, basic validation
                return image_path.stat().st_size > 1024  # At least 1KB
            else:
                # For regular images, try to load
                img = cv2.imread(str(image_path))
                if img is None:
                    return False
                
                height, width = img.shape[:2]
                return width >= self.min_image_size[0] and height >= self.min_image_size[1]
                
        except Exception as e:
            self.logger.warning(f"⚠️ Error validating {image_path}: {e}")
            return False
    
    def detect_duplicates(self, image_list: List[Dict]) -> Set[str]:
        """Detect duplicate images using file hash"""
        self.logger.info("🔍 Detecting duplicate images...")
        
        hash_map = defaultdict(list)
        duplicates = set()
        
        for img_info in image_list:
            try:
                with open(img_info['path'], 'rb') as f:
                    file_hash = hashlib.md5(f.read()).hexdigest()
                hash_map[file_hash].append(img_info['path'])
            except Exception as e:
                self.logger.warning(f"⚠️ Error hashing {img_info['path']}: {e}")
        
        for file_hash, paths in hash_map.items():
            if len(paths) > 1:
                # Keep the first one, mark others as duplicates
                for duplicate_path in paths[1:]:
                    duplicates.add(str(duplicate_path))
                    self.stats['duplicates_found'] += 1
        
        self.logger.info(f"🔍 Found {len(duplicates)} duplicate images")
        return duplicates
    
    def intelligent_classification(self, image_info: Dict, folder_analysis: Dict) -> Dict:
        """
        Intelligently classify an image based on multiple factors
        تصنيف ذكي للصورة بناءً على عوامل متعددة
        """
        classification = {
            'pathologies': [],
            'confidence': 0.0,
            'reasoning': []
        }
        
        # Factor 1: Folder-based classification
        folder_pathology = folder_analysis.get('predicted_pathology', 'normal')
        folder_confidence = folder_analysis.get('confidence', 0.0)
        
        if folder_pathology != 'normal':
            classification['pathologies'].append({
                'name': folder_pathology,
                'confidence': folder_confidence,
                'source': 'folder_analysis'
            })
            classification['reasoning'].append(f"Folder suggests {folder_pathology}")
        
        # Factor 2: Filename-based classification
        filename = image_info['name'].lower()
        for pathology, patterns in self.pathology_patterns.items():
            for keyword in patterns['keywords']:
                if keyword.lower() in filename:
                    classification['pathologies'].append({
                        'name': pathology,
                        'confidence': patterns['confidence'],
                        'source': 'filename_analysis'
                    })
                    classification['reasoning'].append(f"Filename contains '{keyword}'")
                    break
        
        # If no pathology detected, classify as normal
        if not classification['pathologies']:
            classification['pathologies'].append({
                'name': 'normal',
                'confidence': 0.5,
                'source': 'default'
            })
            classification['reasoning'].append("No pathology indicators found")
        
        # Calculate overall confidence
        if classification['pathologies']:
            classification['confidence'] = max(p['confidence'] for p in classification['pathologies'])
        
        return classification
    
    def organize_intelligently(self, train_ratio: float = 0.7, val_ratio: float = 0.2, test_ratio: float = 0.1):
        """
        Main intelligent organization function
        وظيفة التنظيم الذكي الرئيسية
        """
        self.logger.info("🤖 Starting intelligent data organization...")
        
        # Step 1: Explore directory structure
        exploration_map = self.explore_directory_structure()
        
        if not exploration_map:
            self.logger.error("❌ No data found to organize!")
            return
        
        # Step 2: Collect all valid images
        all_images = []
        for folder_path, folder_analysis in exploration_map.items():
            for img_info in folder_analysis['images']:
                img_info['folder_analysis'] = folder_analysis
                all_images.append(img_info)
        
        self.logger.info(f"📊 Found {len(all_images)} valid images across {len(exploration_map)} folders")
        
        # Step 3: Detect and remove duplicates
        duplicates = self.detect_duplicates(all_images)
        unique_images = [img for img in all_images if str(img['path']) not in duplicates]
        
        self.logger.info(f"📊 After removing duplicates: {len(unique_images)} unique images")
        
        # Step 4: Intelligent classification
        classified_images = []
        for img_info in unique_images:
            classification = self.intelligent_classification(img_info, img_info['folder_analysis'])
            img_info['classification'] = classification
            classified_images.append(img_info)
            
            # Update statistics
            for pathology in classification['pathologies']:
                self.stats['pathology_distribution'][pathology['name']] += 1
        
        # Step 5: Smart data splitting
        train_images, val_images, test_images = self.smart_data_split(
            classified_images, train_ratio, val_ratio, test_ratio
        )
        
        # Step 6: Copy and organize files
        train_annotations = self.process_and_copy_images(train_images, "train")
        val_annotations = self.process_and_copy_images(val_images, "val")
        test_annotations = self.process_and_copy_images(test_images, "test")
        
        # Step 7: Save annotations and reports
        self.save_annotations(train_annotations, "train_annotations.json")
        self.save_annotations(val_annotations, "val_annotations.json")
        self.save_annotations(test_annotations, "test_annotations.json")
        
        # Step 8: Generate comprehensive report
        self.generate_intelligence_report(exploration_map, train_annotations, val_annotations, test_annotations)
        
        self.logger.info("🎉 Intelligent data organization completed successfully!")
    
    def smart_data_split(self, images: List[Dict], train_ratio: float, val_ratio: float, test_ratio: float) -> Tuple[List, List, List]:
        """
        Smart data splitting ensuring balanced pathology distribution
        تقسيم ذكي للبيانات يضمن توزيع متوازن للأمراض
        """
        self.logger.info("🧠 Performing smart data split...")
        
        # Group images by pathology
        pathology_groups = defaultdict(list)
        for img in images:
            primary_pathology = img['classification']['pathologies'][0]['name']
            pathology_groups[primary_pathology].append(img)
        
        train_images, val_images, test_images = [], [], []
        
        # Split each pathology group proportionally
        for pathology, group_images in pathology_groups.items():
            random.shuffle(group_images)
            
            n_total = len(group_images)
            n_train = int(n_total * train_ratio)
            n_val = int(n_total * val_ratio)
            
            train_images.extend(group_images[:n_train])
            val_images.extend(group_images[n_train:n_train + n_val])
            test_images.extend(group_images[n_train + n_val:])
            
            self.logger.info(f"📊 {pathology}: {n_train} train, {len(group_images[n_train:n_train + n_val])} val, {len(group_images[n_train + n_val:])} test")
        
        return train_images, val_images, test_images
    
    def process_and_copy_images(self, images: List[Dict], split_name: str) -> List[Dict]:
        """Process and copy images to target directory"""
        annotations = []
        target_images_dir = self.target_dir / split_name / "images"
        
        self.logger.info(f"📁 Processing {split_name} set with {len(images)} images...")
        
        for i, img_info in enumerate(images):
            # Generate new image name
            new_name = f"{split_name}_{i+1:06d}"
            
            # Copy image
            source_path = img_info['path']
            target_path = target_images_dir / f"{new_name}{source_path.suffix}"
            
            try:
                shutil.copy2(source_path, target_path)
                
                # Create annotation
                annotation = {
                    "image_id": new_name,
                    "image_path": f"images/{target_path.name}",
                    "original_path": str(source_path),
                    "original_folder": str(source_path.parent.relative_to(self.source_dir)),
                    "pathologies": img_info['classification']['pathologies'],
                    "classification_reasoning": img_info['classification']['reasoning'],
                    "severity_score": self.calculate_severity_score(img_info['classification']),
                    "confidence_score": img_info['classification']['confidence'],
                    "mask_path": None,
                    "metadata": {
                        "file_size": img_info['size'],
                        "extension": img_info['extension'],
                        "folder_keywords": img_info['folder_analysis'].get('folder_keywords', [])
                    },
                    "created_at": datetime.now().isoformat()
                }
                
                annotations.append(annotation)
                
            except Exception as e:
                self.logger.error(f"❌ Error copying {source_path}: {e}")
        
        return annotations
    
    def calculate_severity_score(self, classification: Dict) -> float:
        """Calculate severity score based on pathology type and confidence"""
        if not classification['pathologies']:
            return 0.0
        
        primary_pathology = classification['pathologies'][0]
        pathology_name = primary_pathology['name']
        confidence = primary_pathology['confidence']
        
        # Severity mapping
        severity_map = {
            'normal': 0.0,
            'caries': 0.6,
            'infection': 0.8,
            'fracture': 0.7,
            'cyst': 0.5,
            'tumor': 0.9
        }
        
        base_severity = severity_map.get(pathology_name, 0.5)
        return min(1.0, base_severity * confidence)
    
    def save_annotations(self, annotations: List[Dict], filename: str):
        """Save annotations to JSON file"""
        output_path = self.target_dir / filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(annotations, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"💾 Saved {len(annotations)} annotations to {output_path}")
    
    def generate_intelligence_report(self, exploration_map: Dict, train_ann: List, val_ann: List, test_ann: List):
        """Generate comprehensive intelligence report"""
        report = {
            "agent_analysis": {
                "timestamp": datetime.now().isoformat(),
                "source_directory": str(self.source_dir),
                "target_directory": str(self.target_dir),
                "exploration_summary": {
                    "total_folders_explored": self.stats['total_folders_explored'],
                    "total_files_found": self.stats['total_files_found'],
                    "valid_images": self.stats['valid_images'],
                    "invalid_images": self.stats['invalid_images'],
                    "duplicates_removed": self.stats['duplicates_found']
                }
            },
            "folder_structure_analysis": exploration_map,
            "pathology_distribution": dict(self.stats['pathology_distribution']),
            "data_splits": {
                "train": {"count": len(train_ann), "percentage": len(train_ann) / (len(train_ann) + len(val_ann) + len(test_ann)) * 100},
                "validation": {"count": len(val_ann), "percentage": len(val_ann) / (len(train_ann) + len(val_ann) + len(test_ann)) * 100},
                "test": {"count": len(test_ann), "percentage": len(test_ann) / (len(train_ann) + len(val_ann) + len(test_ann)) * 100}
            },
            "quality_metrics": {
                "average_confidence": sum(ann['confidence_score'] for ann in train_ann + val_ann + test_ann) / (len(train_ann) + len(val_ann) + len(test_ann)),
                "pathology_balance": self.calculate_pathology_balance()
            }
        }
        
        # Save report
        report_path = self.target_dir / "intelligence_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        self.print_intelligence_summary(report)
        
        self.logger.info(f"📄 Intelligence report saved to: {report_path}")
    
    def calculate_pathology_balance(self) -> Dict:
        """Calculate how balanced the pathology distribution is"""
        total = sum(self.stats['pathology_distribution'].values())
        if total == 0:
            return {}
        
        balance = {}
        for pathology, count in self.stats['pathology_distribution'].items():
            balance[pathology] = {
                "count": count,
                "percentage": (count / total) * 100
            }
        
        return balance
    
    def print_intelligence_summary(self, report: Dict):
        """Print a beautiful summary of the intelligence analysis"""
        print("\n" + "="*60)
        print("🤖 INTELLIGENT DATA ORGANIZATION REPORT")
        print("="*60)
        
        exploration = report['agent_analysis']['exploration_summary']
        print(f"📁 Folders Explored: {exploration['total_folders_explored']}")
        print(f"📄 Files Found: {exploration['total_files_found']}")
        print(f"✅ Valid Images: {exploration['valid_images']}")
        print(f"❌ Invalid Images: {exploration['invalid_images']}")
        print(f"🔄 Duplicates Removed: {exploration['duplicates_removed']}")
        
        print(f"\n📊 DATA DISTRIBUTION:")
        for split, data in report['data_splits'].items():
            print(f"   {split.capitalize()}: {data['count']} images ({data['percentage']:.1f}%)")
        
        print(f"\n🦷 PATHOLOGY DISTRIBUTION:")
        for pathology, data in report['quality_metrics']['pathology_balance'].items():
            print(f"   {pathology.capitalize()}: {data['count']} images ({data['percentage']:.1f}%)")
        
        print(f"\n📈 QUALITY METRICS:")
        print(f"   Average Confidence: {report['quality_metrics']['average_confidence']:.2f}")
        
        print("="*60)


def main():
    """Main function to run the intelligent data agent"""
    print("🤖 AI Dental Assistant - Intelligent Data Organization Agent")
    print("=" * 60)
    
    # Initialize the intelligent agent
    agent = IntelligentDataAgent()
    
    # Check if source directory exists
    if not agent.source_dir.exists():
        print(f"❌ Source directory '{agent.source_dir}' does not exist!")
        print("Please create a 'sets' folder and put your dental X-ray images there.")
        return
    
    print(f"🔍 Source: {agent.source_dir}")
    print(f"🎯 Target: {agent.target_dir}")
    
    # Ask for confirmation
    confirm = input("\n🚀 Start intelligent data organization? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ Operation cancelled.")
        return
    
    try:
        # Run intelligent organization
        agent.organize_intelligently()
        
        print("\n🎉 Intelligent data organization completed successfully!")
        print(f"📁 Organized data available in: {agent.target_dir}")
        print("📄 Check the intelligence_report.json for detailed analysis")
        print("\n🚀 Ready to train your AI models!")
        
    except Exception as e:
        print(f"❌ Error during organization: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
