"""
Security utilities for authentication and authorization
أدوات الأمان للمصادقة والتخويل
"""

from datetime import datetime, timedelta
from typing import Optional, Union
import jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from backend.app.config import settings
from backend.database.connection import get_db
from backend.database.models import User

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Security scheme
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plain password against its hash
    التحقق من كلمة مرور عادية مقابل تشفيرها
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Generate password hash
    إنتاج تشفير كلمة المرور
    """
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token
    إنشاء رمز وصول JWT
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

    return encoded_jwt


def verify_token(token: str) -> dict:
    """
    Verify and decode JWT token
    التحقق من رمز JWT وفك تشفيره
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "Token has expired",
                "message_ar": "انتهت صلاحية الرمز"
            }
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "Invalid token",
                "message_ar": "رمز غير صالح"
            }
        )


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    Get current authenticated user
    الحصول على المستخدم المصادق عليه حالياً
    """
    token = credentials.credentials
    payload = verify_token(token)

    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "Invalid token",
                "message_ar": "رمز غير صالح"
            }
        )

    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "User not found",
                "message_ar": "المستخدم غير موجود"
            }
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "User account is disabled",
                "message_ar": "حساب المستخدم معطل"
            }
        )

    return user


def validate_tunisian_id(national_id: str) -> bool:
    """
    Validate Tunisian national ID format
    التحقق من صيغة الهوية الوطنية التونسية
    """
    if not national_id or len(national_id) != 8:
        return False

    # Check if all characters are digits
    if not national_id.isdigit():
        return False

    # Basic validation - you can add more sophisticated checks
    return True


def validate_cnam_number(cnam_number: str) -> bool:
    """
    Validate CNAM social security number format
    التحقق من صيغة رقم الضمان الاجتماعي CNAM
    """
    if not cnam_number:
        return False

    # Remove any spaces or dashes
    clean_number = cnam_number.replace(" ", "").replace("-", "")

    # CNAM numbers are typically 13 digits
    if len(clean_number) != 13 or not clean_number.isdigit():
        return False

    return True


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for secure file uploads
    تنظيف اسم الملف للتحميل الآمن
    """
    import re
    import os

    # Remove path components
    filename = os.path.basename(filename)

    # Remove or replace dangerous characters
    filename = re.sub(r'[^\w\-_\.]', '_', filename)

    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext

    return filename


def generate_secure_filename(original_filename: str, user_id: int) -> str:
    """
    Generate secure filename with timestamp and user ID
    إنتاج اسم ملف آمن مع الطابع الزمني ومعرف المستخدم
    """
    import uuid
    import os

    # Get file extension
    _, ext = os.path.splitext(original_filename)

    # Generate unique filename
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]

    secure_filename = f"user_{user_id}_{timestamp}_{unique_id}{ext}"

    return secure_filename


class PermissionChecker:
    """
    Class for checking user permissions
    فئة للتحقق من صلاحيات المستخدم
    """

    @staticmethod
    def can_access_patient(user_role: str, user_id: int, patient_dentist_id: int) -> bool:
        """Check if user can access patient data"""
        if user_role == "admin":
            return True

        if user_role in ["dentist", "assistant"] and user_id == patient_dentist_id:
            return True

        return False

    @staticmethod
    def can_modify_patient(user_role: str, user_id: int, patient_dentist_id: int) -> bool:
        """Check if user can modify patient data"""
        if user_role == "admin":
            return True

        if user_role == "dentist" and user_id == patient_dentist_id:
            return True

        return False

    @staticmethod
    def can_delete_record(user_role: str) -> bool:
        """Check if user can delete records"""
        return user_role in ["admin", "dentist"]

    @staticmethod
    def can_access_analytics(user_role: str) -> bool:
        """Check if user can access analytics"""
        return user_role in ["admin", "dentist"]


def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """
    Mask sensitive data for logging/display
    إخفاء البيانات الحساسة للسجلات/العرض
    """
    if not data or len(data) <= visible_chars:
        return mask_char * len(data) if data else ""

    return data[:visible_chars] + mask_char * (len(data) - visible_chars)


def validate_file_type(filename: str, allowed_extensions: list) -> bool:
    """
    Validate file type based on extension
    التحقق من نوع الملف بناءً على الامتداد
    """
    import os

    _, ext = os.path.splitext(filename.lower())
    return ext in [e.lower() for e in allowed_extensions]


def check_file_size(file_size: int, max_size: int = None) -> bool:
    """
    Check if file size is within limits
    التحقق من أن حجم الملف ضمن الحدود المسموحة
    """
    max_size = max_size or settings.MAX_FILE_SIZE
    return file_size <= max_size
