# Docker Compose for AI Dental Assistant
# تكوين Docker Compose لمساعد طبيب الأسنان الذكي

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: dental_ai_postgres
    environment:
      POSTGRES_DB: dental_ai_db
      POSTGRES_USER: dental_user
      POSTGRES_PASSWORD: dental_pass
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - dental_ai_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dental_user -d dental_ai_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: dental_ai_redis
    command: redis-server --appendonly yes --requirepass dental_redis_pass
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - dental_ai_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: dental_ai_backend
    environment:
      - DATABASE_URL=**************************************************/dental_ai_db
      - REDIS_URL=redis://:dental_redis_pass@redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-in-production
      - ENVIRONMENT=development
      - DEBUG=true
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads
      - models_data:/app/models
      - logs_data:/app/logs
    ports:
      - "8000:8000"
    networks:
      - dental_ai_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: dental_ai_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_ENVIRONMENT=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    networks:
      - dental_ai_network
    depends_on:
      - backend
    restart: unless-stopped
    stdin_open: true
    tty: true

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: dental_ai_nginx
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - uploads_data:/var/www/uploads
    ports:
      - "80:80"
      - "443:443"
    networks:
      - dental_ai_network
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    profiles:
      - production

  # AI Model Service (Optional separate service)
  ai_service:
    build:
      context: ./ai-models
      dockerfile: Dockerfile
    container_name: dental_ai_models
    environment:
      - MODEL_PATH=/app/models/dental_resnet.pth
      - DEVICE=cpu
    volumes:
      - models_data:/app/models
      - ./ai-models:/app
    ports:
      - "8001:8001"
    networks:
      - dental_ai_network
    restart: unless-stopped
    profiles:
      - ai-service

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  models_data:
    driver: local
  logs_data:
    driver: local

networks:
  dental_ai_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
