#!/bin/bash

# AI Dental Assistant Startup Script
# سكريبت بدء تشغيل مساعد طبيب الأسنان الذكي

echo "🦷 Starting AI Dental Assistant..."
echo "🦷 بدء تشغيل مساعد طبيب الأسنان الذكي..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "❌ Docker غير مثبت. يرجى تثبيت Docker أولاً."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "❌ Docker Compose غير مثبت. يرجى تثبيت Docker Compose أولاً."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    echo "📝 إنشاء ملف .env من القالب..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update the configuration."
    echo "✅ تم إنشاء ملف .env. يرجى مراجعة وتحديث التكوين."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
echo "📁 إنشاء المجلدات الضرورية..."
mkdir -p uploads models logs static temp

# Start the services
echo "🚀 Starting services with Docker Compose..."
echo "🚀 بدء الخدمات باستخدام Docker Compose..."

# Pull latest images
docker-compose pull

# Build and start services
docker-compose up -d --build

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
echo "⏳ انتظار جاهزية الخدمات..."
sleep 30

# Check service health
echo "🔍 Checking service health..."
echo "🔍 فحص صحة الخدمات..."

# Check backend
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Backend is healthy"
    echo "✅ الخادم الخلفي يعمل بشكل صحيح"
else
    echo "❌ Backend is not responding"
    echo "❌ الخادم الخلفي لا يستجيب"
fi

# Check frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is healthy"
    echo "✅ الواجهة الأمامية تعمل بشكل صحيح"
else
    echo "❌ Frontend is not responding"
    echo "❌ الواجهة الأمامية لا تستجيب"
fi

echo ""
echo "🎉 AI Dental Assistant is starting up!"
echo "🎉 مساعد طبيب الأسنان الذكي يبدأ التشغيل!"
echo ""
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Documentation: http://localhost:8000/docs"
echo ""
echo "To stop the services, run: docker-compose down"
echo "لإيقاف الخدمات، قم بتشغيل: docker-compose down"
