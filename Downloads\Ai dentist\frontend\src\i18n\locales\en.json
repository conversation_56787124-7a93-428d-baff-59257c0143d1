{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "view": "View", "download": "Download", "upload": "Upload", "select": "Select", "clear": "Clear", "reset": "Reset", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "profile": "Profile", "dashboard": "Dashboard", "patients": "Patients", "analysis": "Analysis", "reports": "Reports", "telemedicine": "Telemedicine", "notifications": "Notifications", "language": "Language", "theme": "Theme", "date": "Date", "time": "Time", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "status": "Status", "type": "Type", "category": "Category", "description": "Description", "notes": "Notes", "actions": "Actions", "details": "Details", "summary": "Summary", "total": "Total", "count": "Count", "percentage": "Percentage", "average": "Average", "minimum": "Minimum", "maximum": "Maximum", "required": "Required", "optional": "Optional", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "online": "Online", "offline": "Offline", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information"}, "auth": {"welcome": "Welcome to AI Dental Assistant", "loginTitle": "<PERSON><PERSON>", "registerTitle": "Create New Account", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "firstNameAr": "First Name in Arabic", "lastNameAr": "Last Name in Arabic", "licenseNumber": "License Number", "specialization": "Specialization", "clinicName": "Clinic Name", "clinicAddress": "Clinic Address", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "createAccount": "Create Account", "signIn": "Sign In", "signUp": "Sign Up", "loginSuccess": "Login successful", "registerSuccess": "Registration successful", "logoutSuccess": "Logout successful", "invalidCredentials": "Invalid credentials", "accountCreated": "Account created successfully", "passwordChanged": "Password changed successfully", "profileUpdated": "Profile updated successfully"}, "navigation": {"dashboard": "Dashboard", "patients": "Patients", "addPatient": "Add Patient", "patientList": "Patient List", "analysis": "Analysis", "xrayAnalysis": "X-ray Analysis", "analysisHistory": "Analysis History", "reports": "Reports", "statisticalReports": "Statistical Reports", "medicalReports": "Medical Reports", "telemedicine": "Telemedicine", "consultations": "Consultations", "videoCall": "Video Call", "settings": "Settings", "profile": "Profile", "preferences": "Preferences", "security": "Security", "help": "Help", "documentation": "Documentation", "support": "Support", "about": "About Application"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome, {{name}}", "todayStats": "Today's Statistics", "totalPatients": "Total Patients", "totalAnalyses": "Total Analyses", "pendingReviews": "Pending Reviews", "urgentCases": "Urgent Cases", "recentActivity": "Recent Activity", "recentAnalyses": "Recent Analyses", "upcomingAppointments": "Upcoming Appointments", "quickActions": "Quick Actions", "addNewPatient": "Add New Patient", "analyzeXray": "Analyze X-ray", "viewReports": "View Reports", "startConsultation": "Start Consultation"}, "patients": {"title": "Patient Management", "addPatient": "Add New Patient", "editPatient": "Edit Patient Data", "patientDetails": "Patient Details", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "medicalInfo": "Medical Information", "firstName": "First Name", "lastName": "Last Name", "firstNameAr": "First Name in Arabic", "lastNameAr": "Last Name in Arabic", "dateOfBirth": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "nationalId": "National ID Number", "cnamNumber": "Social Security Number", "phone": "Phone Number", "email": "Email Address", "address": "Address", "city": "City", "postalCode": "Postal Code", "medicalHistory": "Medical History", "allergies": "Allergies", "currentMedications": "Current Medications", "emergencyContact": "Emergency Contact", "consent": "Consent", "consentGiven": "Consent Given", "dataProcessingConsent": "Data Processing Consent", "researchConsent": "Research Consent", "preferredLanguage": "Preferred Language", "patientCreated": "Patient record created successfully", "patientUpdated": "Patient data updated successfully", "patientDeleted": "Patient record deleted successfully", "searchPatients": "Search Patients", "noPatients": "No patient records", "patientCount": "Patient count: {{count}}"}, "analysis": {"title": "X-ray Analysis", "uploadXray": "Upload X-ray Image", "analyzeImage": "Analyze Image", "analysisResults": "Analysis Results", "aiPredictions": "AI Predictions", "dentistReview": "Dentist Review", "diagnosis": "Diagnosis", "treatment": "Treatment", "recommendations": "Recommendations", "confidence": "Confidence Level", "severity": "Severity Level", "low": "Low", "medium": "Medium", "high": "High", "critical": "Critical", "pathologies": "Detected Pathologies", "caries": "<PERSON><PERSON>", "infection": "Infection", "fracture": "Fracture", "cyst": "<PERSON><PERSON>", "tumor": "<PERSON><PERSON>", "normal": "Normal", "uploadProgress": "Upload Progress", "analysisProgress": "Analysis Progress", "processingTime": "Processing Time", "modelVersion": "Model Version", "imageFormat": "Image Format", "imageSize": "Image Size", "heatmap": "Heatmap", "showHeatmap": "Show Heatmap", "hideHeatmap": "Hide Heatmap", "reanalyze": "Re-analyze", "reviewed": "Reviewed", "notReviewed": "Not Reviewed", "urgent": "<PERSON><PERSON>", "followUp": "Follow-up Required", "treatmentPriority": "Treatment Priority", "analysisCreated": "Analysis created successfully", "analysisUpdated": "Analysis updated successfully", "analysisDeleted": "Analysis deleted successfully", "reanalysisCompleted": "Re-analysis completed successfully", "noAnalyses": "No analyses", "analysisCount": "Analysis count: {{count}}"}, "reports": {"title": "Reports and Statistics", "generateReport": "Generate Report", "reportType": "Report Type", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "patientReport": "Patient Report", "analysisReport": "Analysis Report", "statisticalReport": "Statistical Report", "medicalReport": "Medical Report", "exportPdf": "Export PDF", "exportExcel": "Export Excel", "exportCsv": "Export CSV", "totalAnalyses": "Total Analyses", "pathologiesDetected": "Pathologies Detected", "severityDistribution": "Severity Distribution", "averageProcessingTime": "Average Processing Time", "reviewedPercentage": "Reviewed Percentage", "urgentCases": "Urgent Cases", "reportGenerated": "Report generated successfully", "reportExported": "Report exported successfully"}, "telemedicine": {"title": "Telemedicine", "consultations": "Consultations", "scheduleConsultation": "Schedule Consultation", "startConsultation": "Start Consultation", "endConsultation": "End Consultation", "consultationType": "Consultation Type", "videoCall": "Video Call", "audioCall": "Audio Call", "chat": "Text Chat", "consultationDate": "Consultation Date", "duration": "Duration", "status": "Status", "scheduled": "Scheduled", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "chiefComplaint": "Chief <PERSON><PERSON><PERSON><PERSON>", "consultationNotes": "Consultation Notes", "prescription": "Prescription", "followUpRequired": "Follow-up Required", "videoQuality": "Video Quality", "connectionIssues": "Connection Issues", "consultationCreated": "Consultation created successfully", "consultationUpdated": "Consultation updated successfully", "consultationStarted": "Consultation started", "consultationEnded": "Consultation ended"}, "settings": {"title": "Settings", "profile": "Profile", "preferences": "Preferences", "security": "Security", "notifications": "Notifications", "privacy": "Privacy", "display": "Display", "analysis": "Analysis", "backup": "Backup", "language": "Language", "theme": "Theme", "lightTheme": "Light Theme", "darkTheme": "Dark Theme", "autoTheme": "Auto", "fontSize": "Font Size", "small": "Small", "medium": "Medium", "large": "Large", "highContrast": "High Contrast", "dateFormat": "Date Format", "timeFormat": "Time Format", "timezone": "Timezone", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "pushNotifications": "Push Notifications", "urgentCaseNotifications": "Urgent Case Notifications", "followUpReminders": "Follow-up Reminders", "systemUpdates": "System Updates", "dataSharing": "Data Sharing", "analyticsTracking": "Analytics Tracking", "researchParticipation": "Research Participation", "marketingCommunications": "Marketing Communications", "confidenceThreshold": "Confidence Threshold", "autoReview": "Auto Review", "showHeatmaps": "Show Heatmaps", "saveOriginalImages": "Save Original Images", "compressionLevel": "Compression Level", "twoFactorAuth": "Two-Factor Authentication", "sessionTimeout": "Session Timeout", "passwordExpiry": "Password Expiry", "loginNotifications": "Login Notifications", "deviceTracking": "<PERSON>ce Tracking", "autoBackup": "Auto Backup", "backupFrequency": "Backup Frequency", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "backupLocation": "Backup Location", "local": "Local", "cloud": "Cloud", "both": "Both", "retentionPeriod": "Retention Period", "settingsSaved": "Setting<PERSON> saved successfully", "settingsReset": "Settings reset", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password"}, "errors": {"general": "An unexpected error occurred", "networkError": "Network error", "serverError": "Server error", "authenticationFailed": "Authentication failed", "accessDenied": "Access denied", "notFound": "Not found", "validationError": "Validation error", "fileUploadError": "File upload error", "analysisError": "Analysis error", "databaseError": "Database error", "connectionTimeout": "Connection timeout", "invalidFileFormat": "Invalid file format", "fileTooLarge": "File too large", "insufficientPermissions": "Insufficient permissions", "sessionExpired": "Session expired", "invalidCredentials": "Invalid credentials", "accountLocked": "Account locked", "passwordTooWeak": "Password too weak", "emailAlreadyExists": "Email already exists", "usernameAlreadyExists": "Username already exists", "patientNotFound": "Patient not found", "analysisNotFound": "Analysis not found", "reportGenerationFailed": "Report generation failed", "backupFailed": "Backup failed", "restoreFailed": "Rest<PERSON> failed"}, "success": {"operationCompleted": "Operation completed successfully", "dataSaved": "Data saved successfully", "fileUploaded": "File uploaded successfully", "analysisCompleted": "Analysis completed successfully", "reportGenerated": "Report generated successfully", "backupCompleted": "Backup completed successfully", "restoreCompleted": "Restore completed successfully", "settingsUpdated": "Settings updated successfully", "passwordChanged": "Password changed successfully", "profileUpdated": "Profile updated successfully", "emailSent": "<PERSON>ail sent successfully", "notificationSent": "Notification sent successfully"}}