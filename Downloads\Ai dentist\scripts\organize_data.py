#!/usr/bin/env python3
"""
Data Organization Script for AI Dental Assistant
سكريبت تنظيم البيانات لمساعد طبيب الأسنان الذكي

This script organizes unstructured dental X-ray images from a 'sets' folder
into the proper training structure required by the AI models.
"""

import os
import shutil
import json
import random
from pathlib import Path
from typing import List, Dict, Tuple
import cv2
import numpy as np
from datetime import datetime

class DentalDataOrganizer:
    """
    Organizes dental X-ray data for training
    منظم بيانات الأشعة السينية للأسنان للتدريب
    """
    
    def __init__(self, source_dir: str = "sets", target_dir: str = "ai-models/data"):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        
        # Supported image formats
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.dcm', '.dicom'}
        
        # Pathology keywords for automatic classification
        self.pathology_keywords = {
            'caries': ['caries', 'cavity', 'decay', 'تسوس', 'carie'],
            'infection': ['infection', 'abscess', 'pus', 'عدوى', 'التهاب'],
            'fracture': ['fracture', 'break', 'crack', 'كسر', 'شرخ'],
            'cyst': ['cyst', 'كيس', 'kyste'],
            'tumor': ['tumor', 'mass', 'growth', 'ورم', 'tumeur']
        }
        
        # Create target directories
        self.create_directory_structure()
    
    def create_directory_structure(self):
        """Create the required directory structure"""
        directories = [
            self.target_dir / "train" / "images",
            self.target_dir / "train" / "masks",
            self.target_dir / "val" / "images", 
            self.target_dir / "val" / "masks",
            self.target_dir / "test" / "images",
            self.target_dir / "test" / "masks"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        print(f"✅ Created directory structure in {self.target_dir}")
    
    def find_all_images(self) -> List[Path]:
        """Find all image files in the source directory recursively"""
        images = []
        
        if not self.source_dir.exists():
            print(f"❌ Source directory '{self.source_dir}' does not exist!")
            return images
        
        print(f"🔍 Scanning for images in {self.source_dir}...")
        
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                file_path = Path(root) / file
                if file_path.suffix.lower() in self.image_extensions:
                    images.append(file_path)
        
        print(f"📊 Found {len(images)} images")
        return images
    
    def classify_image(self, image_path: Path) -> List[str]:
        """
        Classify image based on filename and folder structure
        تصنيف الصورة بناءً على اسم الملف وهيكل المجلد
        """
        pathologies = []
        
        # Check filename and parent directories for pathology keywords
        search_text = f"{image_path.name} {image_path.parent.name}".lower()
        
        for pathology, keywords in self.pathology_keywords.items():
            for keyword in keywords:
                if keyword.lower() in search_text:
                    pathologies.append(pathology)
                    break
        
        # If no pathology detected, classify as 'normal'
        if not pathologies:
            pathologies = ['normal']
        
        return pathologies
    
    def validate_image(self, image_path: Path) -> bool:
        """Validate if image can be loaded and processed"""
        try:
            if image_path.suffix.lower() in ['.dcm', '.dicom']:
                # For DICOM files, we'll assume they're valid
                return True
            else:
                # For regular images, try to load with OpenCV
                img = cv2.imread(str(image_path))
                return img is not None and img.size > 0
        except Exception as e:
            print(f"⚠️ Invalid image {image_path}: {e}")
            return False
    
    def copy_and_rename_image(self, source_path: Path, target_dir: Path, 
                             new_name: str) -> Path:
        """Copy image to target directory with new name"""
        target_path = target_dir / f"{new_name}{source_path.suffix}"
        
        # Handle duplicate names
        counter = 1
        while target_path.exists():
            target_path = target_dir / f"{new_name}_{counter}{source_path.suffix}"
            counter += 1
        
        shutil.copy2(source_path, target_path)
        return target_path
    
    def organize_data(self, train_ratio: float = 0.7, val_ratio: float = 0.2, 
                     test_ratio: float = 0.1):
        """
        Main function to organize the data
        الوظيفة الرئيسية لتنظيم البيانات
        """
        print("🚀 Starting data organization...")
        
        # Find all images
        all_images = self.find_all_images()
        
        if not all_images:
            print("❌ No images found to organize!")
            return
        
        # Validate images
        valid_images = []
        for img_path in all_images:
            if self.validate_image(img_path):
                valid_images.append(img_path)
            else:
                print(f"⚠️ Skipping invalid image: {img_path}")
        
        print(f"✅ {len(valid_images)} valid images found")
        
        # Shuffle images for random distribution
        random.shuffle(valid_images)
        
        # Calculate split indices
        total_images = len(valid_images)
        train_end = int(total_images * train_ratio)
        val_end = train_end + int(total_images * val_ratio)
        
        # Split data
        train_images = valid_images[:train_end]
        val_images = valid_images[train_end:val_end]
        test_images = valid_images[val_end:]
        
        print(f"📊 Data split:")
        print(f"   Training: {len(train_images)} images")
        print(f"   Validation: {len(val_images)} images") 
        print(f"   Test: {len(test_images)} images")
        
        # Process each split
        train_annotations = self.process_image_set(train_images, "train")
        val_annotations = self.process_image_set(val_images, "val")
        test_annotations = self.process_image_set(test_images, "test")
        
        # Save annotations
        self.save_annotations(train_annotations, "train_annotations.json")
        self.save_annotations(val_annotations, "val_annotations.json")
        self.save_annotations(test_annotations, "test_annotations.json")
        
        # Generate summary report
        self.generate_summary_report(train_annotations, val_annotations, test_annotations)
        
        print("🎉 Data organization completed successfully!")
    
    def process_image_set(self, images: List[Path], split_name: str) -> List[Dict]:
        """Process a set of images (train/val/test)"""
        annotations = []
        target_images_dir = self.target_dir / split_name / "images"
        
        print(f"📁 Processing {split_name} set...")
        
        for i, img_path in enumerate(images):
            # Generate new image name
            new_name = f"{split_name}_{i+1:06d}"
            
            # Copy image
            new_img_path = self.copy_and_rename_image(img_path, target_images_dir, new_name)
            
            # Classify pathologies
            pathologies = self.classify_image(img_path)
            
            # Create annotation
            annotation = {
                "image_id": new_name,
                "image_path": f"images/{new_img_path.name}",
                "original_path": str(img_path),
                "pathologies": [
                    {
                        "name": pathology,
                        "confidence": 1.0 if pathology != 'normal' else 0.0,
                        "location": {"x": 0, "y": 0, "width": 0, "height": 0}
                    }
                    for pathology in pathologies
                ],
                "severity_score": 0.5 if 'normal' not in pathologies else 0.0,
                "mask_path": None,
                "created_at": datetime.now().isoformat()
            }
            
            annotations.append(annotation)
        
        return annotations
    
    def save_annotations(self, annotations: List[Dict], filename: str):
        """Save annotations to JSON file"""
        output_path = self.target_dir / filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(annotations, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Saved {len(annotations)} annotations to {output_path}")
    
    def generate_summary_report(self, train_ann: List[Dict], 
                               val_ann: List[Dict], test_ann: List[Dict]):
        """Generate a summary report of the organized data"""
        
        def count_pathologies(annotations):
            pathology_counts = {}
            for ann in annotations:
                for pathology in ann['pathologies']:
                    name = pathology['name']
                    pathology_counts[name] = pathology_counts.get(name, 0) + 1
            return pathology_counts
        
        train_counts = count_pathologies(train_ann)
        val_counts = count_pathologies(val_ann)
        test_counts = count_pathologies(test_ann)
        
        report = {
            "organization_date": datetime.now().isoformat(),
            "total_images": len(train_ann) + len(val_ann) + len(test_ann),
            "splits": {
                "train": {
                    "count": len(train_ann),
                    "pathologies": train_counts
                },
                "validation": {
                    "count": len(val_ann),
                    "pathologies": val_counts
                },
                "test": {
                    "count": len(test_ann),
                    "pathologies": test_counts
                }
            },
            "pathology_distribution": {
                pathology: {
                    "train": train_counts.get(pathology, 0),
                    "val": val_counts.get(pathology, 0),
                    "test": test_counts.get(pathology, 0),
                    "total": (train_counts.get(pathology, 0) + 
                             val_counts.get(pathology, 0) + 
                             test_counts.get(pathology, 0))
                }
                for pathology in set(list(train_counts.keys()) + 
                                   list(val_counts.keys()) + 
                                   list(test_counts.keys()))
            }
        }
        
        # Save report
        report_path = self.target_dir / "organization_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n📊 ORGANIZATION SUMMARY:")
        print("=" * 50)
        print(f"Total images organized: {report['total_images']}")
        print(f"Training images: {len(train_ann)}")
        print(f"Validation images: {len(val_ann)}")
        print(f"Test images: {len(test_ann)}")
        print("\nPathology Distribution:")
        for pathology, counts in report['pathology_distribution'].items():
            print(f"  {pathology}: {counts['total']} total "
                  f"(Train: {counts['train']}, Val: {counts['val']}, Test: {counts['test']})")
        print(f"\n📄 Detailed report saved to: {report_path}")


def main():
    """Main function to run the data organization"""
    print("🦷 AI Dental Assistant - Data Organization Tool")
    print("=" * 50)
    
    # Get source directory from user
    source_dir = input("Enter the path to your 'sets' folder (or press Enter for 'sets'): ").strip()
    if not source_dir:
        source_dir = "sets"
    
    # Initialize organizer
    organizer = DentalDataOrganizer(source_dir=source_dir)
    
    # Check if source directory exists
    if not organizer.source_dir.exists():
        print(f"❌ Source directory '{source_dir}' does not exist!")
        print("Please make sure your images are in a folder named 'sets' or provide the correct path.")
        return
    
    # Ask for confirmation
    print(f"\n📁 Source: {organizer.source_dir}")
    print(f"📁 Target: {organizer.target_dir}")
    
    confirm = input("\nProceed with data organization? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ Operation cancelled.")
        return
    
    # Run organization
    try:
        organizer.organize_data()
        print("\n✅ Data organization completed successfully!")
        print(f"📁 Organized data is available in: {organizer.target_dir}")
        print("\n🚀 You can now start training your AI models!")
        
    except Exception as e:
        print(f"❌ Error during organization: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
