"""
Pydantic schemas for patient management
مخططات Pydantic لإدارة المرضى
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional, Dict, Any
from datetime import datetime, date


class PatientBase(BaseModel):
    """Base patient schema"""
    first_name: str
    last_name: str
    first_name_ar: Optional[str] = None
    last_name_ar: Optional[str] = None
    date_of_birth: Optional[date] = None
    gender: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    preferred_language: str = "ar"
    
    @validator('gender')
    def validate_gender(cls, v):
        if v and v not in ['male', 'female', 'other']:
            raise ValueError('Gender must be one of: male, female, other')
        return v
    
    @validator('preferred_language')
    def validate_language(cls, v):
        if v not in ['ar', 'fr', 'en']:
            raise ValueError('Language must be one of: ar, fr, en')
        return v


class PatientCreate(PatientBase):
    """Schema for patient creation"""
    national_id: Optional[str] = None
    cnam_number: Optional[str] = None
    medical_history: Optional[Dict[str, Any]] = {}
    allergies: Optional[str] = None
    current_medications: Optional[str] = None
    emergency_contact: Optional[Dict[str, Any]] = {}
    consent_given: bool = False
    data_processing_consent: bool = False
    research_consent: bool = False
    
    @validator('national_id')
    def validate_national_id(cls, v):
        if v and (len(v) != 8 or not v.isdigit()):
            raise ValueError('Tunisian national ID must be 8 digits')
        return v


class PatientUpdate(BaseModel):
    """Schema for patient updates"""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    first_name_ar: Optional[str] = None
    last_name_ar: Optional[str] = None
    date_of_birth: Optional[date] = None
    gender: Optional[str] = None
    national_id: Optional[str] = None
    cnam_number: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    medical_history: Optional[Dict[str, Any]] = None
    allergies: Optional[str] = None
    current_medications: Optional[str] = None
    emergency_contact: Optional[Dict[str, Any]] = None
    preferred_language: Optional[str] = None
    
    @validator('gender')
    def validate_gender(cls, v):
        if v and v not in ['male', 'female', 'other']:
            raise ValueError('Gender must be one of: male, female, other')
        return v
    
    @validator('national_id')
    def validate_national_id(cls, v):
        if v and (len(v) != 8 or not v.isdigit()):
            raise ValueError('Tunisian national ID must be 8 digits')
        return v
    
    @validator('preferred_language')
    def validate_language(cls, v):
        if v and v not in ['ar', 'fr', 'en']:
            raise ValueError('Language must be one of: ar, fr, en')
        return v


class PatientResponse(PatientBase):
    """Schema for patient response"""
    id: int
    uuid: str
    national_id: Optional[str] = None
    cnam_number: Optional[str] = None
    medical_history: Dict[str, Any]
    allergies: Optional[str] = None
    current_medications: Optional[str] = None
    emergency_contact: Dict[str, Any]
    consent_given: bool
    consent_date: Optional[datetime] = None
    data_processing_consent: bool
    research_consent: bool
    dentist_id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class PatientSearch(BaseModel):
    """Schema for patient search"""
    query: str
    search_fields: Optional[list] = ["name", "national_id", "phone"]
    
    @validator('search_fields')
    def validate_search_fields(cls, v):
        allowed_fields = ["name", "national_id", "phone", "email"]
        for field in v:
            if field not in allowed_fields:
                raise ValueError(f'Invalid search field: {field}')
        return v


class PatientSummary(BaseModel):
    """Schema for patient summary"""
    id: int
    full_name: str
    full_name_ar: Optional[str] = None
    age: Optional[int] = None
    phone: Optional[str] = None
    last_visit: Optional[datetime] = None
    total_analyses: int
    urgent_cases: int
    
    class Config:
        from_attributes = True


class ConsentUpdate(BaseModel):
    """Schema for updating patient consent"""
    consent_given: Optional[bool] = None
    data_processing_consent: Optional[bool] = None
    research_consent: Optional[bool] = None
    consent_notes: Optional[str] = None
