"""
Enhanced Dental AI Model for Comprehensive X-ray Analysis
نموذج الذكاء الاصطناعي المحسن للتحليل الشامل للأشعة السينية للأسنان
"""

import torch
import torch.nn as nn
import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import json
import base64
from io import BytesIO
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.cm as cm

# Import the enhanced model architecture
import sys
sys.path.append(str(Path(__file__).parent.parent / 'training'))
from model_architecture import (
    MultiTaskDentalNet,
    DentalPathologyType,
    DentalPathologyDetector,
    GradCAMGenerator
)


class EnhancedDentalAIModel:
    """
    Enhanced dental AI model for comprehensive pathology detection
    النموذج المحسن للذكاء الاصطناعي للكشف الشامل عن الأمراض السنية
    """

    def __init__(self, model_path: Optional[str] = None):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.detector = DentalPathologyDetector(model_path)

        # Comprehensive pathology mapping with Arabic and French translations
        self.pathology_info = {
            # Dental Pathologies
            'caries_superficial': {
                'ar': 'تسوس سطحي',
                'fr': 'Carie superficielle',
                'en': 'Superficial Caries',
                'severity': 'low',
                'urgent': False,
                'recommendations': {
                    'ar': 'حشوة بسيطة مطلوبة',
                    'fr': 'Obturation simple requise',
                    'en': 'Simple filling required'
                }
            },
            'caries_deep': {
                'ar': 'تسوس عميق',
                'fr': 'Carie profonde',
                'en': 'Deep Caries',
                'severity': 'high',
                'urgent': True,
                'recommendations': {
                    'ar': 'علاج عصب أو حشوة عميقة',
                    'fr': 'Traitement endodontique ou obturation profonde',
                    'en': 'Root canal treatment or deep filling'
                }
            },
            'periapical_abscess': {
                'ar': 'خراج حول الذروة',
                'fr': 'Abcès périapical',
                'en': 'Periapical Abscess',
                'severity': 'critical',
                'urgent': True,
                'recommendations': {
                    'ar': 'علاج طارئ - مضادات حيوية وعلاج عصب',
                    'fr': 'Traitement urgent - antibiotiques et endodontie',
                    'en': 'Emergency treatment - antibiotics and endodontics'
                }
            },
            'root_fracture': {
                'ar': 'كسر الجذر',
                'fr': 'Fracture radiculaire',
                'en': 'Root Fracture',
                'severity': 'high',
                'urgent': True,
                'recommendations': {
                    'ar': 'فحص سريري فوري وتقييم إمكانية الحفظ',
                    'fr': 'Examen clinique immédiat et évaluation de conservation',
                    'en': 'Immediate clinical examination and conservation assessment'
                }
            },
            'impacted_wisdom': {
                'ar': 'ضرس العقل المنطمر',
                'fr': 'Dent de sagesse incluse',
                'en': 'Impacted Wisdom Tooth',
                'severity': 'medium',
                'urgent': False,
                'recommendations': {
                    'ar': 'تقييم جراحي للاستخراج',
                    'fr': 'Évaluation chirurgicale pour extraction',
                    'en': 'Surgical evaluation for extraction'
                }
            },
            'horizontal_bone_loss': {
                'ar': 'فقدان عظم أفقي',
                'fr': 'Perte osseuse horizontale',
                'en': 'Horizontal Bone Loss',
                'severity': 'medium',
                'urgent': False,
                'recommendations': {
                    'ar': 'علاج لثوي وتنظيف عميق',
                    'fr': 'Traitement parodontal et détartrage profond',
                    'en': 'Periodontal treatment and deep cleaning'
                }
            },
            'defective_crown': {
                'ar': 'تاج معيب',
                'fr': 'Couronne défectueuse',
                'en': 'Defective Crown',
                'severity': 'medium',
                'urgent': False,
                'recommendations': {
                    'ar': 'استبدال التاج',
                    'fr': 'Remplacement de la couronne',
                    'en': 'Crown replacement'
                }
            },
            'odontogenic_tumor': {
                'ar': 'ورم سني المنشأ',
                'fr': 'Tumeur odontogène',
                'en': 'Odontogenic Tumor',
                'severity': 'critical',
                'urgent': True,
                'recommendations': {
                    'ar': 'إحالة فورية لأخصائي جراحة الفم',
                    'fr': 'Référence immédiate au chirurgien oral',
                    'en': 'Immediate referral to oral surgeon'
                }
            }
        }

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        self.logger.info(f"Enhanced Dental AI Model initialized on {self.device}")

    def _load_model(self) -> nn.Module:
        """Load the trained dental AI model"""
        try:
            model = DentalResNet(num_classes=len(self.pathology_classes))

            if Path(self.model_path).exists():
                checkpoint = torch.load(self.model_path, map_location=self.device)
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"✅ Model loaded from {self.model_path}")
            else:
                print(f"⚠️ Model file not found at {self.model_path}, using untrained model")

            model.to(self.device)
            model.eval()
            return model

        except Exception as e:
            print(f"❌ Error loading model: {e}")
            # Return a dummy model for development
            return self._create_dummy_model()

    def _create_dummy_model(self) -> nn.Module:
        """Create a dummy model for development/testing"""
        model = models.resnet50(pretrained=True)
        model.fc = nn.Linear(model.fc.in_features, len(self.pathology_classes))
        model.to(self.device)
        model.eval()
        return model

    def preprocess_image(self, image_path: str) -> Tuple[torch.Tensor, Dict]:
        """
        Preprocess dental X-ray image for analysis
        معالجة صورة الأشعة السينية للأسنان للتحليل
        """
        try:
            # Handle different image formats
            if image_path.lower().endswith(('.dcm', '.dicom')):
                image, metadata = self._load_dicom_image(image_path)
            else:
                image = Image.open(image_path).convert('RGB')
                metadata = {"format": "standard", "source": image_path}

            # Apply preprocessing
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)

            return image_tensor, metadata

        except Exception as e:
            raise ValueError(f"Error preprocessing image: {e}")

    def _load_dicom_image(self, dicom_path: str) -> Tuple[Image.Image, Dict]:
        """Load and convert DICOM image to PIL Image"""
        try:
            dicom_data = pydicom.dcmread(dicom_path)

            # Extract pixel data
            pixel_array = dicom_data.pixel_array

            # Normalize to 0-255 range
            pixel_array = ((pixel_array - pixel_array.min()) /
                          (pixel_array.max() - pixel_array.min()) * 255).astype(np.uint8)

            # Convert to PIL Image
            if len(pixel_array.shape) == 2:  # Grayscale
                image = Image.fromarray(pixel_array, mode='L').convert('RGB')
            else:
                image = Image.fromarray(pixel_array)

            # Extract metadata
            metadata = {
                "format": "DICOM",
                "patient_id": getattr(dicom_data, 'PatientID', 'Unknown'),
                "study_date": getattr(dicom_data, 'StudyDate', 'Unknown'),
                "modality": getattr(dicom_data, 'Modality', 'Unknown'),
                "institution": getattr(dicom_data, 'InstitutionName', 'Unknown'),
                "image_size": pixel_array.shape
            }

            return image, metadata

        except Exception as e:
            raise ValueError(f"Error loading DICOM image: {e}")

    def analyze_xray(self, image_path: str, language: str = "ar") -> Dict:
        """
        Perform complete X-ray analysis
        إجراء تحليل كامل للأشعة السينية
        """
        start_time = time.time()

        try:
            # Preprocess image
            image_tensor, metadata = self.preprocess_image(image_path)

            # Run inference
            with torch.no_grad():
                outputs, attention_maps = self.model(image_tensor, return_cam=True)
                probabilities = torch.softmax(outputs, dim=1)

            # Process results
            predictions = self._process_predictions(probabilities, language)
            heatmap = self._generate_heatmap(image_tensor, attention_maps, image_path)

            processing_time = time.time() - start_time

            # Compile results
            results = {
                "predictions": predictions,
                "metadata": metadata,
                "heatmap_path": heatmap,
                "processing_time": processing_time,
                "model_version": "1.0.0",
                "confidence_threshold": self.confidence_threshold,
                "language": language,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            return results

        except Exception as e:
            raise RuntimeError(f"Error during X-ray analysis: {e}")

    def _process_predictions(self, probabilities: torch.Tensor, language: str) -> List[Dict]:
        """Process model predictions into structured format"""
        predictions = []
        probs = probabilities.cpu().numpy()[0]

        for i, (class_name, prob) in enumerate(zip(self.pathology_classes, probs)):
            if prob > self.confidence_threshold:
                # Get localized terms
                localized_name = self._get_localized_term(class_name, language)
                severity = self._determine_severity(class_name, prob)

                prediction = {
                    "class": class_name,
                    "name": localized_name,
                    "confidence": float(prob),
                    "severity": severity,
                    "severity_localized": SEVERITY_LEVELS[severity][language],
                    "recommendations": self._get_recommendations(class_name, language),
                    "urgent": severity in ["high", "critical"]
                }
                predictions.append(prediction)

        # Sort by confidence
        predictions.sort(key=lambda x: x["confidence"], reverse=True)
        return predictions

    def _get_localized_term(self, class_name: str, language: str) -> str:
        """Get localized medical term"""
        if class_name in MEDICAL_TERMS:
            return MEDICAL_TERMS[class_name].get(language, class_name)
        return class_name

    def _determine_severity(self, class_name: str, confidence: float) -> str:
        """Determine severity level based on pathology type and confidence"""
        high_severity_conditions = ["tumor", "infection", "fracture", "deep_caries"]
        medium_severity_conditions = ["caries", "periapical_lesion", "bone_loss"]

        if class_name in high_severity_conditions:
            return "critical" if confidence > 0.9 else "high"
        elif class_name in medium_severity_conditions:
            return "high" if confidence > 0.8 else "medium"
        else:
            return "low"

    def _get_recommendations(self, class_name: str, language: str) -> List[str]:
        """Get treatment recommendations based on detected pathology"""
        recommendations_db = {
            "caries": {
                "ar": ["حشو الأسنان", "تنظيف الأسنان", "فحص دوري"],
                "fr": ["Obturation dentaire", "Nettoyage dentaire", "Contrôle périodique"],
                "en": ["Dental filling", "Dental cleaning", "Regular checkup"]
            },
            "infection": {
                "ar": ["علاج العدوى", "مضادات حيوية", "متابعة عاجلة"],
                "fr": ["Traitement de l'infection", "Antibiotiques", "Suivi urgent"],
                "en": ["Infection treatment", "Antibiotics", "Urgent follow-up"]
            }
        }

        return recommendations_db.get(class_name, {}).get(language, ["استشارة طبية"])

    def _generate_heatmap(self, image_tensor: torch.Tensor, attention_maps: torch.Tensor,
                         original_path: str) -> str:
        """Generate heatmap visualization for detected pathologies"""
        try:
            # Convert attention maps to numpy
            attention = attention_maps.cpu().numpy()[0]  # First batch item

            # Average across channels if multiple
            if len(attention.shape) > 2:
                attention = np.mean(attention, axis=0)

            # Resize to match original image
            original_image = cv2.imread(original_path)
            h, w = original_image.shape[:2]
            attention_resized = cv2.resize(attention, (w, h))

            # Normalize attention map
            attention_norm = (attention_resized - attention_resized.min()) / \
                           (attention_resized.max() - attention_resized.min())

            # Create heatmap
            heatmap = cv2.applyColorMap((attention_norm * 255).astype(np.uint8),
                                     cv2.COLORMAP_JET)

            # Overlay on original image
            overlay = cv2.addWeighted(original_image, 0.6, heatmap, 0.4, 0)

            # Save heatmap
            heatmap_path = original_path.replace('.', '_heatmap.')
            cv2.imwrite(heatmap_path, overlay)

            return heatmap_path

        except Exception as e:
            print(f"Error generating heatmap: {e}")
            return None


# Global analyzer instance
dental_analyzer = DentalXrayAnalyzer()
