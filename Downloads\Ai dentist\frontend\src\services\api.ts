/**
 * API service layer for AI Dental Assistant
 * طبقة خدمة API لمساعد طبيب الأسنان الذكي
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Base API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials: { username: string; password: string }) =>
    apiClient.post('/api/v1/auth/login', credentials),

  register: (userData: any) =>
    apiClient.post('/api/v1/auth/register', userData),

  getCurrentUser: () =>
    apiClient.get('/api/v1/auth/me'),

  updateProfile: (userData: any) =>
    apiClient.put('/api/v1/auth/me', userData),

  changePassword: (passwordData: { current_password: string; new_password: string }) =>
    apiClient.post('/api/v1/auth/change-password', passwordData),

  logout: () =>
    apiClient.post('/api/v1/auth/logout'),
};

// Patients API
export const patientsAPI = {
  getPatients: (params?: { skip?: number; limit?: number; search?: string }) =>
    apiClient.get('/api/v1/patients', { params }),

  getPatientById: (patientId: number) =>
    apiClient.get(`/api/v1/patients/${patientId}`),

  createPatient: (patientData: any) =>
    apiClient.post('/api/v1/patients', patientData),

  updatePatient: (patientId: number, patientData: any) =>
    apiClient.put(`/api/v1/patients/${patientId}`, patientData),

  deletePatient: (patientId: number) =>
    apiClient.delete(`/api/v1/patients/${patientId}`),

  updateConsent: (patientId: number, consentData: any) =>
    apiClient.post(`/api/v1/patients/${patientId}/consent`, consentData),
};

// Analysis API
export const analysisAPI = {
  uploadAndAnalyze: (formData: FormData, config?: AxiosRequestConfig) =>
    apiClient.post('/api/v1/analysis/upload-xray', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    }),

  getPatientAnalyses: (patientId: number, params?: { skip?: number; limit?: number }) =>
    apiClient.get(`/api/v1/analysis/patient/${patientId}/analyses`, { params }),

  getAnalysisById: (analysisId: number) =>
    apiClient.get(`/api/v1/analysis/analysis/${analysisId}`),

  updateAnalysisReview: (analysisId: number, reviewData: any) =>
    apiClient.put(`/api/v1/analysis/analysis/${analysisId}/review`, reviewData),

  deleteAnalysis: (analysisId: number) =>
    apiClient.delete(`/api/v1/analysis/analysis/${analysisId}`),

  reanalyzeXray: (analysisId: number, language: string = 'ar') =>
    apiClient.post(`/api/v1/analysis/reanalyze/${analysisId}`, { language }),

  getStatistics: () =>
    apiClient.get('/api/v1/analysis/statistics'),
};

// Enhanced Analysis API
export const enhancedAnalysisAPI = {
  comprehensiveAnalysis: (formData: FormData, config?: AxiosRequestConfig) =>
    apiClient.post('/api/v1/enhanced-analysis/comprehensive', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    }),

  batchAnalysis: (formData: FormData, config?: AxiosRequestConfig) =>
    apiClient.post('/api/v1/enhanced-analysis/batch-analysis', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    }),

  getSupportedPathologies: (language: string = 'ar') =>
    apiClient.get('/api/v1/enhanced-analysis/pathologies', { params: { language } }),

  getDetailedReport: (analysisId: string, language: string = 'ar', format: string = 'json') =>
    apiClient.get(`/api/v1/enhanced-analysis/analysis/${analysisId}/report`, {
      params: { language, format }
    }),

  getModelInfo: () =>
    apiClient.get('/api/v1/enhanced-analysis/model/info'),
};

// Reports API
export const reportsAPI = {
  getDashboardStats: () =>
    apiClient.get('/api/v1/reports/dashboard-stats'),

  getReports: (params?: any) =>
    apiClient.get('/api/v1/reports', { params }),

  generateReport: (reportType: string, params: any) =>
    apiClient.post(`/api/v1/reports/generate/${reportType}`, params),

  exportReport: (reportId: string, format: string) =>
    apiClient.get(`/api/v1/reports/${reportId}/export/${format}`, {
      responseType: 'blob',
    }),
};

// Telemedicine API
export const telemedicineAPI = {
  getConsultations: (params?: any) =>
    apiClient.get('/api/v1/telemedicine', { params }),

  createConsultation: (consultationData: any) =>
    apiClient.post('/api/v1/telemedicine', consultationData),

  updateConsultation: (consultationId: number, consultationData: any) =>
    apiClient.put(`/api/v1/telemedicine/${consultationId}`, consultationData),

  deleteConsultation: (consultationId: number) =>
    apiClient.delete(`/api/v1/telemedicine/${consultationId}`),

  startConsultation: (consultationId: number) =>
    apiClient.post(`/api/v1/telemedicine/${consultationId}/start`),

  endConsultation: (consultationId: number) =>
    apiClient.post(`/api/v1/telemedicine/${consultationId}/end`),
};

// System API
export const systemAPI = {
  getHealth: () =>
    apiClient.get('/health'),

  getSystemInfo: () =>
    apiClient.get('/api/v1/info'),

  getAuditLogs: (params?: any) =>
    apiClient.get('/api/v1/system/audit-logs', { params }),

  exportData: (params: any) =>
    apiClient.post('/api/v1/system/export-data', params, {
      responseType: 'blob',
    }),

  importData: (formData: FormData) =>
    apiClient.post('/api/v1/system/import-data', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),
};

// File upload utility
export const uploadFile = (
  file: File,
  endpoint: string,
  onProgress?: (progress: number) => void
): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);

  return apiClient.post(endpoint, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
  });
};

// Download file utility
export const downloadFile = async (
  url: string,
  filename: string
): Promise<void> => {
  try {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('Download failed:', error);
    throw error;
  }
};

// Error handling utility
export const handleApiError = (error: any): string => {
  if (error.response?.data?.detail) {
    if (typeof error.response.data.detail === 'string') {
      return error.response.data.detail;
    }
    if (error.response.data.detail.message) {
      return error.response.data.detail.message;
    }
  }

  if (error.response?.data?.message) {
    return error.response.data.message;
  }

  if (error.message) {
    return error.message;
  }

  return 'An unexpected error occurred';
};

// API client instance for direct use
export default apiClient;
