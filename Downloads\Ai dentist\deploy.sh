#!/bin/bash

# AI Dental Assistant Production Deployment Script
# سكريبت نشر مساعد طبيب الأسنان الذكي في الإنتاج

set -e

echo "🦷 AI Dental Assistant - Production Deployment"
echo "🦷 مساعد طبيب الأسنان الذكي - النشر في الإنتاج"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check prerequisites
print_status "Checking prerequisites..."

# Check Docker
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running. Please start Docker first."
    exit 1
fi

print_success "Prerequisites check passed"

# Environment setup
print_status "Setting up environment..."

if [ ! -f .env ]; then
    if [ -f .env.example ]; then
        cp .env.example .env
        print_warning ".env file created from template. Please review and update the configuration."
        print_warning "ملف .env تم إنشاؤه من القالب. يرجى مراجعة وتحديث التكوين."
    else
        print_error ".env.example file not found. Cannot create .env file."
        exit 1
    fi
else
    print_success ".env file already exists"
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p uploads models logs static temp
mkdir -p data/postgres data/redis
print_success "Directories created"

# Set proper permissions
print_status "Setting permissions..."
chmod 755 uploads models logs static temp
chmod 755 data/postgres data/redis
print_success "Permissions set"

# Pull latest images
print_status "Pulling latest Docker images..."
docker-compose pull
print_success "Images pulled successfully"

# Build custom images
print_status "Building application images..."
docker-compose build --no-cache
print_success "Images built successfully"

# Start services
print_status "Starting services..."
docker-compose up -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 30

# Health checks
print_status "Performing health checks..."

# Check PostgreSQL
if docker-compose exec -T postgres pg_isready -U dental_user -d dental_ai_db &> /dev/null; then
    print_success "PostgreSQL is healthy"
else
    print_error "PostgreSQL is not responding"
    docker-compose logs postgres
    exit 1
fi

# Check Redis
if docker-compose exec -T redis redis-cli ping &> /dev/null; then
    print_success "Redis is healthy"
else
    print_error "Redis is not responding"
    docker-compose logs redis
    exit 1
fi

# Check Backend
if curl -f http://localhost:8000/health &> /dev/null; then
    print_success "Backend API is healthy"
else
    print_warning "Backend API is not responding yet, checking logs..."
    docker-compose logs backend
fi

# Check Frontend
if curl -f http://localhost:3000 &> /dev/null; then
    print_success "Frontend is healthy"
else
    print_warning "Frontend is not responding yet, checking logs..."
    docker-compose logs frontend
fi

# Database migrations
print_status "Running database migrations..."
docker-compose exec backend alembic upgrade head
print_success "Database migrations completed"

# Create default admin user (optional)
read -p "Do you want to create a default admin user? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Creating default admin user..."
    docker-compose exec backend python -c "
from app.database.connection import get_db
from app.database.models import User
from app.utils.security import get_password_hash
from sqlalchemy.orm import Session
import uuid

db = next(get_db())
admin_user = User(
    uuid=str(uuid.uuid4()),
    email='<EMAIL>',
    username='admin',
    hashed_password=get_password_hash('admin123'),
    first_name='Admin',
    last_name='User',
    role='admin',
    is_active=True,
    is_verified=True
)
db.add(admin_user)
db.commit()
print('Admin user created: <EMAIL> / admin123')
"
    print_success "Default admin user created"
    print_warning "Default credentials: <EMAIL> / admin123"
    print_warning "Please change the default password immediately!"
fi

# SSL Certificate setup (optional)
read -p "Do you want to set up SSL certificates? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Setting up SSL certificates..."
    mkdir -p docker/nginx/ssl
    
    # Generate self-signed certificate for development
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout docker/nginx/ssl/dental-ai.key \
        -out docker/nginx/ssl/dental-ai.crt \
        -subj "/C=TN/ST=Tunis/L=Tunis/O=Dental AI/CN=localhost"
    
    print_success "Self-signed SSL certificate generated"
    print_warning "For production, replace with a valid SSL certificate"
fi

# Backup setup
print_status "Setting up backup directories..."
mkdir -p backups/database backups/uploads
chmod 755 backups/database backups/uploads
print_success "Backup directories created"

# Final status check
print_status "Final system status check..."
echo
echo "=== Service Status ==="
docker-compose ps
echo

# Display access information
echo "🎉 Deployment completed successfully!"
echo "🎉 تم النشر بنجاح!"
echo
echo "📱 Access Information:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8000"
echo "   API Documentation: http://localhost:8000/docs"
echo "   Database: localhost:5432 (dental_ai_db)"
echo "   Redis: localhost:6379"
echo
echo "🔧 Management Commands:"
echo "   View logs: docker-compose logs -f [service]"
echo "   Stop services: docker-compose down"
echo "   Restart services: docker-compose restart"
echo "   Update application: git pull && docker-compose up -d --build"
echo
echo "📊 Monitoring:"
echo "   System status: docker-compose ps"
echo "   Resource usage: docker stats"
echo "   Health check: curl http://localhost:8000/health"
echo
echo "🔒 Security Notes:"
echo "   - Change default admin password immediately"
echo "   - Review .env file for production settings"
echo "   - Set up proper SSL certificates for production"
echo "   - Configure firewall rules"
echo "   - Set up regular backups"
echo
echo "📚 Documentation:"
echo "   - README.md: Project overview"
echo "   - DEVELOPMENT.md: Development guide"
echo "   - PROJECT_SUMMARY.md: Complete project summary"
echo
echo "🆘 Support:"
echo "   - Check logs: docker-compose logs [service]"
echo "   - Restart services: docker-compose restart"
echo "   - Full reset: docker-compose down -v && ./deploy.sh"
echo

print_success "AI Dental Assistant is now running!"
print_success "مساعد طبيب الأسنان الذكي يعمل الآن!"
