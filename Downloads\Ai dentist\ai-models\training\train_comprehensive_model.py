"""
Comprehensive training script for advanced dental pathology detection
سكريبت التدريب الشامل للكشف المتقدم عن الأمراض السنية
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import cv2
from pathlib import Path
import json
import logging
from typing import Dict, List, Tuple, Optional
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import albumentations as A
from albumentations.pytorch import ToTensorV2

from model_architecture import (
    MultiTaskDentalNet, 
    DentalPathologyType, 
    DentalPathologyDetector
)


class DentalXrayDataset(Dataset):
    """
    Comprehensive dental X-ray dataset for multi-pathology detection
    مجموعة بيانات شاملة للأشعة السينية للأسنان للكشف متعدد الأمراض
    """
    
    def __init__(
        self, 
        data_dir: str, 
        annotations_file: str,
        transform: Optional[A.Compose] = None,
        mode: str = 'train'
    ):
        self.data_dir = Path(data_dir)
        self.mode = mode
        self.transform = transform
        
        # Load annotations
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        self.pathology_names = [p.value for p in DentalPathologyType]
        self.num_classes = len(self.pathology_names)
        
        # Create pathology to index mapping
        self.pathology_to_idx = {name: idx for idx, name in enumerate(self.pathology_names)}
        
    def __len__(self):
        return len(self.annotations)
    
    def __getitem__(self, idx):
        annotation = self.annotations[idx]
        
        # Load image
        image_path = self.data_dir / annotation['image_path']
        image = cv2.imread(str(image_path), cv2.IMREAD_GRAYSCALE)
        
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")
        
        # Convert to RGB (3 channels) for pretrained models
        image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        
        # Apply transforms
        if self.transform:
            transformed = self.transform(image=image)
            image = transformed['image']
        
        # Prepare multi-label targets
        pathology_labels = np.zeros(self.num_classes, dtype=np.float32)
        
        # Set labels for detected pathologies
        for pathology in annotation.get('pathologies', []):
            if pathology['name'] in self.pathology_to_idx:
                idx = self.pathology_to_idx[pathology['name']]
                pathology_labels[idx] = 1.0
        
        # Severity score (0-1)
        severity_score = annotation.get('severity_score', 0.0)
        
        # Localization mask (if available)
        mask_path = annotation.get('mask_path')
        if mask_path:
            mask = cv2.imread(str(self.data_dir / mask_path), cv2.IMREAD_GRAYSCALE)
            mask = cv2.resize(mask, (512, 512)) / 255.0
        else:
            mask = np.zeros((512, 512), dtype=np.float32)
        
        return {
            'image': image,
            'pathology_labels': torch.tensor(pathology_labels),
            'severity_score': torch.tensor([severity_score], dtype=torch.float32),
            'localization_mask': torch.tensor(mask, dtype=torch.float32),
            'image_id': annotation['image_id']
        }


class MultiTaskLoss(nn.Module):
    """
    Multi-task loss function for comprehensive dental pathology detection
    دالة الخسارة متعددة المهام للكشف الشامل عن الأمراض السنية
    """
    
    def __init__(self, weights: Dict[str, float] = None):
        super().__init__()
        
        # Default weights for different tasks
        self.weights = weights or {
            'pathology': 1.0,
            'severity': 0.5,
            'localization': 0.3,
            'confidence': 0.2
        }
        
        # Loss functions
        self.pathology_loss = nn.BCEWithLogitsLoss()
        self.severity_loss = nn.MSELoss()
        self.localization_loss = nn.BCELoss()
        self.confidence_loss = nn.MSELoss()
    
    def forward(self, predictions: Dict, targets: Dict) -> Dict:
        """Calculate multi-task loss"""
        
        # Pathology classification loss
        pathology_loss = self.pathology_loss(
            predictions['pathology_logits'], 
            targets['pathology_labels']
        )
        
        # Severity regression loss
        severity_loss = self.severity_loss(
            predictions['severity_scores'], 
            targets['severity_score']
        )
        
        # Localization loss
        localization_loss = self.localization_loss(
            predictions['localization_maps'], 
            targets['localization_mask']
        )
        
        # Confidence loss (based on pathology prediction accuracy)
        pathology_probs = torch.sigmoid(predictions['pathology_logits'])
        pathology_accuracy = torch.mean(
            (pathology_probs > 0.5).float() == targets['pathology_labels']
        )
        confidence_target = pathology_accuracy.unsqueeze(0).expand_as(predictions['confidence_scores'])
        confidence_loss = self.confidence_loss(
            predictions['confidence_scores'], 
            confidence_target
        )
        
        # Total weighted loss
        total_loss = (
            self.weights['pathology'] * pathology_loss +
            self.weights['severity'] * severity_loss +
            self.weights['localization'] * localization_loss +
            self.weights['confidence'] * confidence_loss
        )
        
        return {
            'total_loss': total_loss,
            'pathology_loss': pathology_loss,
            'severity_loss': severity_loss,
            'localization_loss': localization_loss,
            'confidence_loss': confidence_loss
        }


class DentalTrainer:
    """
    Comprehensive trainer for dental pathology detection model
    مدرب شامل لنموذج الكشف عن الأمراض السنية
    """
    
    def __init__(
        self,
        model: MultiTaskDentalNet,
        train_loader: DataLoader,
        val_loader: DataLoader,
        device: torch.device,
        config: Dict
    ):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        self.config = config
        
        # Loss function
        self.criterion = MultiTaskLoss(config.get('loss_weights'))
        
        # Optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config['learning_rate'],
            weight_decay=config['weight_decay']
        )
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config['num_epochs'],
            eta_min=config['min_lr']
        )
        
        # Metrics tracking
        self.train_losses = []
        self.val_losses = []
        self.best_val_loss = float('inf')
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def train_epoch(self) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        total_losses = {
            'total_loss': 0.0,
            'pathology_loss': 0.0,
            'severity_loss': 0.0,
            'localization_loss': 0.0,
            'confidence_loss': 0.0
        }
        
        num_batches = len(self.train_loader)
        
        for batch_idx, batch in enumerate(tqdm(self.train_loader, desc="Training")):
            # Move data to device
            images = batch['image'].to(self.device)
            targets = {
                'pathology_labels': batch['pathology_labels'].to(self.device),
                'severity_score': batch['severity_score'].to(self.device),
                'localization_mask': batch['localization_mask'].to(self.device)
            }
            
            # Forward pass
            self.optimizer.zero_grad()
            predictions = self.model(images)
            
            # Calculate loss
            losses = self.criterion(predictions, targets)
            
            # Backward pass
            losses['total_loss'].backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # Accumulate losses
            for key, value in losses.items():
                total_losses[key] += value.item()
        
        # Average losses
        for key in total_losses:
            total_losses[key] /= num_batches
        
        return total_losses
    
    def validate(self) -> Dict[str, float]:
        """Validate the model"""
        self.model.eval()
        total_losses = {
            'total_loss': 0.0,
            'pathology_loss': 0.0,
            'severity_loss': 0.0,
            'localization_loss': 0.0,
            'confidence_loss': 0.0
        }
        
        num_batches = len(self.val_loader)
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc="Validation"):
                # Move data to device
                images = batch['image'].to(self.device)
                targets = {
                    'pathology_labels': batch['pathology_labels'].to(self.device),
                    'severity_score': batch['severity_score'].to(self.device),
                    'localization_mask': batch['localization_mask'].to(self.device)
                }
                
                # Forward pass
                predictions = self.model(images)
                
                # Calculate loss
                losses = self.criterion(predictions, targets)
                
                # Accumulate losses
                for key, value in losses.items():
                    total_losses[key] += value.item()
        
        # Average losses
        for key in total_losses:
            total_losses[key] /= num_batches
        
        return total_losses
    
    def train(self):
        """Complete training loop"""
        self.logger.info("Starting comprehensive dental pathology detection training...")
        
        for epoch in range(self.config['num_epochs']):
            self.logger.info(f"Epoch {epoch + 1}/{self.config['num_epochs']}")
            
            # Train
            train_losses = self.train_epoch()
            self.train_losses.append(train_losses)
            
            # Validate
            val_losses = self.validate()
            self.val_losses.append(val_losses)
            
            # Update learning rate
            self.scheduler.step()
            
            # Log progress
            self.logger.info(
                f"Train Loss: {train_losses['total_loss']:.4f}, "
                f"Val Loss: {val_losses['total_loss']:.4f}, "
                f"LR: {self.optimizer.param_groups[0]['lr']:.6f}"
            )
            
            # Save best model
            if val_losses['total_loss'] < self.best_val_loss:
                self.best_val_loss = val_losses['total_loss']
                self.save_checkpoint(epoch, is_best=True)
            
            # Save regular checkpoint
            if (epoch + 1) % self.config['save_every'] == 0:
                self.save_checkpoint(epoch)
        
        self.logger.info("Training completed!")
    
    def save_checkpoint(self, epoch: int, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_loss': self.best_val_loss,
            'config': self.config
        }
        
        filename = 'best_model.pth' if is_best else f'checkpoint_epoch_{epoch}.pth'
        torch.save(checkpoint, filename)
        
        if is_best:
            self.logger.info(f"New best model saved with validation loss: {self.best_val_loss:.4f}")


def get_transforms(mode: str = 'train') -> A.Compose:
    """Get data augmentation transforms"""
    if mode == 'train':
        return A.Compose([
            A.Resize(512, 512),
            A.HorizontalFlip(p=0.5),
            A.Rotate(limit=15, p=0.5),
            A.RandomBrightnessContrast(p=0.5),
            A.GaussNoise(p=0.3),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
    else:
        return A.Compose([
            A.Resize(512, 512),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])


def main():
    """Main training function"""
    # Configuration
    config = {
        'batch_size': 16,
        'num_epochs': 100,
        'learning_rate': 1e-4,
        'weight_decay': 1e-5,
        'min_lr': 1e-6,
        'save_every': 10,
        'loss_weights': {
            'pathology': 1.0,
            'severity': 0.5,
            'localization': 0.3,
            'confidence': 0.2
        }
    }
    
    # Device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Datasets
    train_dataset = DentalXrayDataset(
        data_dir='data/train',
        annotations_file='data/train_annotations.json',
        transform=get_transforms('train'),
        mode='train'
    )
    
    val_dataset = DentalXrayDataset(
        data_dir='data/val',
        annotations_file='data/val_annotations.json',
        transform=get_transforms('val'),
        mode='val'
    )
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Model
    model = MultiTaskDentalNet(num_pathologies=len(DentalPathologyType))
    
    # Trainer
    trainer = DentalTrainer(model, train_loader, val_loader, device, config)
    
    # Start training
    trainer.train()


if __name__ == "__main__":
    main()
