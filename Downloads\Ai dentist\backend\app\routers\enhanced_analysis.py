"""
Enhanced X-ray Analysis API with comprehensive pathology detection
واجهة برمجة التطبيقات المحسنة لتحليل الأشعة السينية مع الكشف الشامل عن الأمراض
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import Optional, List, Dict
import uuid
import json
import asyncio
from datetime import datetime
import logging

from backend.database.connection import get_db
from backend.database.models import User, Patient, XrayAnalysis
from backend.app.schemas.enhanced_analysis import (
    EnhancedAnalysisResponse,
    ComprehensivePathologyResult,
    TemporalAnalysisResult,
    ClinicalReportResult
)
from backend.app.utils.security import get_current_user
from backend.app.utils.file_handler import save_uploaded_file, validate_image_file
from backend.app.utils.audit import log_analysis_activity

# Import enhanced AI model
import sys
from pathlib import Path
ai_models_path = Path(__file__).parent.parent.parent.parent / 'ai-models' / 'inference'
sys.path.append(str(ai_models_path))

# Mock AI model for development (replace with actual import when model is ready)
class MockEnhancedDentalAIModel:
    def analyze_xray_comprehensive(self, image_path, language="ar", include_heatmap=True, previous_image_path=None):
        return {
            "detected_pathologies": [
                {
                    "pathology_id": "caries_deep",
                    "name": "تسوس عميق" if language == "ar" else "Deep Caries",
                    "probability": 0.85,
                    "confidence": 0.92,
                    "severity": "high",
                    "urgent": True,
                    "recommendations": "علاج عصب أو حشوة عميقة" if language == "ar" else "Root canal or deep filling"
                }
            ],
            "overall_severity": 0.75,
            "confidence_score": 0.88,
            "risk_level": "high",
            "requires_urgent_attention": True,
            "visualizations": {
                "localization_heatmap": "data:image/png;base64,mock_heatmap",
                "grad_cam_heatmap": "data:image/png;base64,mock_gradcam"
            } if include_heatmap else {},
            "clinical_report": {
                "title": "تقرير تحليل الأشعة السينية" if language == "ar" else "X-ray Analysis Report",
                "summary": "تم اكتشاف تسوس عميق" if language == "ar" else "Deep caries detected",
                "risk_level": "high",
                "confidence_score": 0.88,
                "findings": [],
                "requires_urgent_attention": True,
                "generated_at": "2024-01-15T10:30:00"
            }
        }

    def get_supported_pathologies(self, language="ar"):
        return [
            {
                "id": "caries_deep",
                "name": "تسوس عميق" if language == "ar" else "Deep Caries",
                "severity": "high",
                "urgent": True
            }
        ]

    def get_model_info(self):
        return {
            "model_type": "Mock Enhanced Dental AI",
            "supported_pathologies": 30,
            "supported_languages": ["ar", "fr", "en"],
            "features": ["Multi-pathology detection", "Heatmap visualization"],
            "device": "cpu",
            "model_loaded": True
        }

# Use mock model for now
EnhancedDentalAIModel = MockEnhancedDentalAIModel

router = APIRouter(prefix="/api/v1/enhanced-analysis", tags=["Enhanced Analysis"])
logger = logging.getLogger(__name__)

# Initialize enhanced AI model
ai_model = EnhancedDentalAIModel()


@router.post("/comprehensive", response_model=EnhancedAnalysisResponse)
async def comprehensive_xray_analysis(
    file: UploadFile = File(...),
    patient_id: str = Form(...),
    language: str = Form(default="ar"),
    include_heatmap: bool = Form(default=True),
    previous_analysis_id: Optional[str] = Form(default=None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Comprehensive X-ray analysis with enhanced AI capabilities
    تحليل شامل للأشعة السينية مع قدرات الذكاء الاصطناعي المحسنة
    """
    try:
        # Validate input
        if not validate_image_file(file):
            raise HTTPException(status_code=400, detail="Invalid image file format")

        # Verify patient exists and user has access
        patient = db.query(Patient).filter(
            Patient.uuid == patient_id,
            Patient.dentist_id == current_user.id
        ).first()

        if not patient:
            raise HTTPException(status_code=404, detail="Patient not found")

        # Save uploaded file
        file_path = await save_uploaded_file(file, "xray_images")

        # Get previous image for temporal analysis if requested
        previous_image_path = None
        if previous_analysis_id:
            previous_analysis = db.query(XrayAnalysis).filter(
                XrayAnalysis.uuid == previous_analysis_id,
                XrayAnalysis.patient_id == patient.id
            ).first()

            if previous_analysis:
                previous_image_path = previous_analysis.image_path

        # Perform comprehensive AI analysis
        analysis_results = await asyncio.to_thread(
            ai_model.analyze_xray_comprehensive,
            file_path,
            language,
            include_heatmap,
            previous_image_path
        )

        # Create analysis record
        analysis_uuid = str(uuid.uuid4())
        analysis_record = XrayAnalysis(
            uuid=analysis_uuid,
            patient_id=patient.id,
            dentist_id=current_user.id,
            image_path=file_path,
            ai_results=analysis_results,
            confidence_score=analysis_results.get('confidence_score', 0.0),
            requires_review=analysis_results.get('requires_urgent_attention', True),
            status='pending_review',
            language=language
        )

        db.add(analysis_record)
        db.commit()
        db.refresh(analysis_record)

        # Log activity for audit
        await log_analysis_activity(
            user_id=current_user.id,
            patient_id=patient.id,
            analysis_id=analysis_record.id,
            action="comprehensive_analysis",
            details={
                "pathologies_detected": len(analysis_results.get('detected_pathologies', [])),
                "risk_level": analysis_results.get('risk_level', 'unknown'),
                "language": language
            }
        )

        # Prepare response
        response = EnhancedAnalysisResponse(
            analysis_id=analysis_uuid,
            patient_id=patient_id,
            status="completed",
            detected_pathologies=[
                ComprehensivePathologyResult(**pathology)
                for pathology in analysis_results.get('detected_pathologies', [])
            ],
            overall_severity=analysis_results.get('overall_severity', 0.0),
            confidence_score=analysis_results.get('confidence_score', 0.0),
            risk_level=analysis_results.get('risk_level', 'low'),
            requires_urgent_attention=analysis_results.get('requires_urgent_attention', False),
            visualizations=analysis_results.get('visualizations', {}),
            clinical_report=ClinicalReportResult(**analysis_results.get('clinical_report', {})),
            language=language,
            analysis_timestamp=datetime.now().isoformat()
        )

        # Add temporal analysis if available
        if 'temporal_analysis' in analysis_results:
            response.temporal_analysis = TemporalAnalysisResult(
                **analysis_results['temporal_analysis']
            )

        return response

    except Exception as e:
        logger.error(f"Error in comprehensive analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.get("/pathologies", response_model=List[Dict])
async def get_supported_pathologies(
    language: str = "ar",
    current_user: User = Depends(get_current_user)
):
    """
    Get list of supported pathologies with translations
    الحصول على قائمة الأمراض المدعومة مع الترجمات
    """
    try:
        pathologies = ai_model.get_supported_pathologies(language)
        return pathologies
    except Exception as e:
        logger.error(f"Error getting pathologies: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve pathologies")


@router.post("/batch-analysis")
async def batch_xray_analysis(
    files: List[UploadFile] = File(...),
    patient_id: str = Form(...),
    language: str = Form(default="ar"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Batch analysis of multiple X-ray images
    تحليل مجموعة من صور الأشعة السينية
    """
    try:
        # Validate patient access
        patient = db.query(Patient).filter(
            Patient.uuid == patient_id,
            Patient.dentist_id == current_user.id
        ).first()

        if not patient:
            raise HTTPException(status_code=404, detail="Patient not found")

        # Limit batch size
        if len(files) > 10:
            raise HTTPException(status_code=400, detail="Maximum 10 files per batch")

        results = []

        for file in files:
            try:
                # Validate and save file
                if not validate_image_file(file):
                    results.append({
                        "filename": file.filename,
                        "status": "error",
                        "error": "Invalid file format"
                    })
                    continue

                file_path = await save_uploaded_file(file, "xray_images")

                # Perform analysis
                analysis_results = await asyncio.to_thread(
                    ai_model.analyze_xray_comprehensive,
                    file_path,
                    language,
                    False  # No heatmap for batch processing
                )

                # Create analysis record
                analysis_uuid = str(uuid.uuid4())
                analysis_record = XrayAnalysis(
                    uuid=analysis_uuid,
                    patient_id=patient.id,
                    dentist_id=current_user.id,
                    image_path=file_path,
                    ai_results=analysis_results,
                    confidence_score=analysis_results.get('confidence_score', 0.0),
                    requires_review=analysis_results.get('requires_urgent_attention', True),
                    status='pending_review',
                    language=language
                )

                db.add(analysis_record)

                results.append({
                    "filename": file.filename,
                    "analysis_id": analysis_uuid,
                    "status": "completed",
                    "pathologies_count": len(analysis_results.get('detected_pathologies', [])),
                    "risk_level": analysis_results.get('risk_level', 'low'),
                    "urgent": analysis_results.get('requires_urgent_attention', False)
                })

            except Exception as e:
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": str(e)
                })

        db.commit()

        # Log batch activity
        await log_analysis_activity(
            user_id=current_user.id,
            patient_id=patient.id,
            analysis_id=None,
            action="batch_analysis",
            details={
                "files_processed": len(files),
                "successful_analyses": len([r for r in results if r["status"] == "completed"]),
                "language": language
            }
        )

        return {
            "batch_id": str(uuid.uuid4()),
            "total_files": len(files),
            "successful": len([r for r in results if r["status"] == "completed"]),
            "failed": len([r for r in results if r["status"] == "error"]),
            "results": results
        }

    except Exception as e:
        logger.error(f"Error in batch analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")


@router.get("/analysis/{analysis_id}/report")
async def get_detailed_report(
    analysis_id: str,
    language: str = "ar",
    format: str = "json",  # json, pdf, html
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed analysis report in various formats
    الحصول على تقرير تحليل مفصل بصيغ مختلفة
    """
    try:
        # Get analysis record
        analysis = db.query(XrayAnalysis).filter(
            XrayAnalysis.uuid == analysis_id,
            XrayAnalysis.dentist_id == current_user.id
        ).first()

        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")

        # Get patient info
        patient = db.query(Patient).filter(Patient.id == analysis.patient_id).first()

        # Prepare comprehensive report
        report_data = {
            "analysis_id": analysis_id,
            "patient_info": {
                "name": f"{patient.first_name} {patient.last_name}",
                "name_ar": f"{patient.first_name_ar} {patient.last_name_ar}",
                "date_of_birth": patient.date_of_birth.isoformat() if patient.date_of_birth else None,
                "national_id": patient.national_id
            },
            "analysis_date": analysis.created_at.isoformat(),
            "dentist_info": {
                "name": f"{current_user.first_name} {current_user.last_name}",
                "license": current_user.license_number
            },
            "ai_results": analysis.ai_results,
            "language": language
        }

        if format == "json":
            return report_data
        elif format == "pdf":
            # Generate PDF report (implement PDF generation)
            pdf_path = await generate_pdf_report(report_data)
            return {"report_url": pdf_path}
        elif format == "html":
            # Generate HTML report
            html_content = await generate_html_report(report_data)
            return {"html_content": html_content}
        else:
            raise HTTPException(status_code=400, detail="Unsupported format")

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Report generation failed: {str(e)}")


@router.get("/model/info")
async def get_model_info(current_user: User = Depends(get_current_user)):
    """
    Get comprehensive AI model information
    الحصول على معلومات شاملة عن نموذج الذكاء الاصطناعي
    """
    try:
        model_info = ai_model.get_model_info()
        return model_info
    except Exception as e:
        logger.error(f"Error getting model info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve model information")


async def generate_pdf_report(report_data: Dict) -> str:
    """Generate PDF report (placeholder implementation)"""
    # Implement PDF generation using reportlab or similar
    # This is a placeholder
    return "/reports/analysis_report.pdf"


async def generate_html_report(report_data: Dict) -> str:
    """Generate HTML report"""
    # Implement HTML report generation
    # This is a placeholder
    html_template = """
    <html>
    <head><title>Dental Analysis Report</title></head>
    <body>
        <h1>تقرير تحليل الأشعة السينية للأسنان</h1>
        <p>Patient: {patient_name}</p>
        <p>Analysis Date: {analysis_date}</p>
        <!-- Add more report content -->
    </body>
    </html>
    """

    return html_template.format(
        patient_name=report_data['patient_info']['name'],
        analysis_date=report_data['analysis_date']
    )
