/**
 * Analysis Page component
 * صفحة التحليل
 */

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
} from '@mui/material';
import { CloudUpload } from '@mui/icons-material';
import { useI18n } from '../../i18n/useI18n';

const AnalysisPage: React.FC = () => {
  const { t } = useI18n();

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          {t('analysis.title')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<CloudUpload />}
          size="large"
        >
          {t('analysis.uploadXray')}
        </Button>
      </Box>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('common.loading')}...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('analysis.title')} - {t('common.loading')}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default AnalysisPage;
