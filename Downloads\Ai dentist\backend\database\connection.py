"""
Database connection and session management
إدارة الاتصال بقاعدة البيانات والجلسات
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import redis
from typing import Generator

from backend.app.config import settings


# PostgreSQL Database Engine
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Redis connection for caching and sessions
redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)


def get_db() -> Generator[Session, None, None]:
    """
    Database dependency for FastAPI
    تبعية قاعدة البيانات لـ FastAPI
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis() -> redis.Redis:
    """
    Redis dependency for caching
    تبعية Redis للتخزين المؤقت
    """
    return redis_client


class DatabaseManager:
    """Database management utilities"""

    @staticmethod
    def create_tables():
        """Create all database tables"""
        from .models import Base
        Base.metadata.create_all(bind=engine)

    @staticmethod
    def drop_tables():
        """Drop all database tables (use with caution!)"""
        from .models import Base
        Base.metadata.drop_all(bind=engine)

    @staticmethod
    def reset_database():
        """Reset database (drop and recreate tables)"""
        DatabaseManager.drop_tables()
        DatabaseManager.create_tables()


class CacheManager:
    """Redis cache management utilities"""

    def __init__(self):
        self.redis = redis_client

    def set(self, key: str, value: str, expire: int = 3600):
        """Set cache value with expiration"""
        return self.redis.setex(key, expire, value)

    def get(self, key: str) -> str:
        """Get cache value"""
        return self.redis.get(key)

    def delete(self, key: str):
        """Delete cache key"""
        return self.redis.delete(key)

    def exists(self, key: str) -> bool:
        """Check if key exists"""
        return self.redis.exists(key)

    def clear_pattern(self, pattern: str):
        """Clear all keys matching pattern"""
        keys = self.redis.keys(pattern)
        if keys:
            return self.redis.delete(*keys)
        return 0


# Global cache manager instance
cache = CacheManager()
