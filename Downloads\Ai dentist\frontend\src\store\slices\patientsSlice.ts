/**
 * Patients slice for Redux store
 * شريحة المرضى لمتجر Redux
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { patientsAPI } from '../../services/api';

// Types
interface Patient {
  id: number;
  uuid: string;
  first_name: string;
  last_name: string;
  first_name_ar?: string;
  last_name_ar?: string;
  date_of_birth?: string;
  gender?: string;
  national_id?: string;
  cnam_number?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  medical_history: any;
  allergies?: string;
  current_medications?: string;
  emergency_contact: any;
  consent_given: boolean;
  consent_date?: string;
  data_processing_consent: boolean;
  research_consent: boolean;
  dentist_id: number;
  is_active: boolean;
  preferred_language: string;
  created_at: string;
  updated_at?: string;
}

interface PatientsState {
  patients: Patient[];
  currentPatient: Patient | null;
  isLoading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
}

interface CreatePatientData {
  first_name: string;
  last_name: string;
  first_name_ar?: string;
  last_name_ar?: string;
  date_of_birth?: string;
  gender?: string;
  national_id?: string;
  cnam_number?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  medical_history?: any;
  allergies?: string;
  current_medications?: string;
  emergency_contact?: any;
  consent_given: boolean;
  data_processing_consent: boolean;
  research_consent: boolean;
  preferred_language?: string;
}

interface UpdatePatientData extends Partial<CreatePatientData> {
  id: number;
}

interface FetchPatientsParams {
  skip?: number;
  limit?: number;
  search?: string;
}

// Initial state
const initialState: PatientsState = {
  patients: [],
  currentPatient: null,
  isLoading: false,
  error: null,
  totalCount: 0,
  currentPage: 0,
  pageSize: 20,
  searchQuery: '',
};

// Async thunks
export const fetchPatients = createAsyncThunk(
  'patients/fetchPatients',
  async (params: FetchPatientsParams = {}, { rejectWithValue }) => {
    try {
      const response = await patientsAPI.getPatients(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch patients');
    }
  }
);

export const fetchPatientById = createAsyncThunk(
  'patients/fetchPatientById',
  async (patientId: number, { rejectWithValue }) => {
    try {
      const response = await patientsAPI.getPatientById(patientId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch patient');
    }
  }
);

export const createPatient = createAsyncThunk(
  'patients/createPatient',
  async (patientData: CreatePatientData, { rejectWithValue }) => {
    try {
      const response = await patientsAPI.createPatient(patientData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to create patient');
    }
  }
);

export const updatePatient = createAsyncThunk(
  'patients/updatePatient',
  async ({ id, ...patientData }: UpdatePatientData, { rejectWithValue }) => {
    try {
      const response = await patientsAPI.updatePatient(id, patientData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to update patient');
    }
  }
);

export const deletePatient = createAsyncThunk(
  'patients/deletePatient',
  async (patientId: number, { rejectWithValue }) => {
    try {
      await patientsAPI.deletePatient(patientId);
      return patientId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to delete patient');
    }
  }
);

export const updatePatientConsent = createAsyncThunk(
  'patients/updateConsent',
  async ({ patientId, consentData }: { patientId: number; consentData: any }, { rejectWithValue }) => {
    try {
      const response = await patientsAPI.updateConsent(patientId, consentData);
      return { patientId, consentData: response };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to update consent');
    }
  }
);

export const searchPatients = createAsyncThunk(
  'patients/searchPatients',
  async (searchQuery: string, { rejectWithValue }) => {
    try {
      const response = await patientsAPI.getPatients({ search: searchQuery });
      return { patients: response, searchQuery };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.detail || 'Search failed');
    }
  }
);

// Patients slice
const patientsSlice = createSlice({
  name: 'patients',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentPatient: (state, action: PayloadAction<Patient | null>) => {
      state.currentPatient = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
    },
    clearPatients: (state) => {
      state.patients = [];
      state.currentPatient = null;
      state.totalCount = 0;
      state.currentPage = 0;
      state.searchQuery = '';
    },
  },
  extraReducers: (builder) => {
    // Fetch patients
    builder
      .addCase(fetchPatients.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPatients.fulfilled, (state, action) => {
        state.isLoading = false;
        state.patients = action.payload;
        state.totalCount = action.payload.length;
        state.error = null;
      })
      .addCase(fetchPatients.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch patient by ID
    builder
      .addCase(fetchPatientById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPatientById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPatient = action.payload;
        state.error = null;
      })
      .addCase(fetchPatientById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create patient
    builder
      .addCase(createPatient.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPatient.fulfilled, (state, action) => {
        state.isLoading = false;
        state.patients.unshift(action.payload);
        state.totalCount += 1;
        state.error = null;
      })
      .addCase(createPatient.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update patient
    builder
      .addCase(updatePatient.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePatient.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.patients.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.patients[index] = action.payload;
        }
        if (state.currentPatient?.id === action.payload.id) {
          state.currentPatient = action.payload;
        }
        state.error = null;
      })
      .addCase(updatePatient.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Delete patient
    builder
      .addCase(deletePatient.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePatient.fulfilled, (state, action) => {
        state.isLoading = false;
        state.patients = state.patients.filter(p => p.id !== action.payload);
        state.totalCount -= 1;
        if (state.currentPatient?.id === action.payload) {
          state.currentPatient = null;
        }
        state.error = null;
      })
      .addCase(deletePatient.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update consent
    builder
      .addCase(updatePatientConsent.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePatientConsent.fulfilled, (state, action) => {
        state.isLoading = false;
        const { patientId, consentData } = action.payload;
        const index = state.patients.findIndex(p => p.id === patientId);
        if (index !== -1) {
          state.patients[index] = { ...state.patients[index], ...consentData.consent_status };
        }
        if (state.currentPatient?.id === patientId) {
          state.currentPatient = { ...state.currentPatient, ...consentData.consent_status };
        }
        state.error = null;
      })
      .addCase(updatePatientConsent.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Search patients
    builder
      .addCase(searchPatients.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchPatients.fulfilled, (state, action) => {
        state.isLoading = false;
        state.patients = action.payload.patients;
        state.searchQuery = action.payload.searchQuery;
        state.totalCount = action.payload.patients.length;
        state.error = null;
      })
      .addCase(searchPatients.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setCurrentPatient,
  setSearchQuery,
  setCurrentPage,
  setPageSize,
  clearPatients,
} = patientsSlice.actions;

export default patientsSlice.reducer;
