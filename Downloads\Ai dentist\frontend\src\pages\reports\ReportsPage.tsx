/**
 * Reports Page component
 * صفحة التقارير
 */

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
} from '@mui/material';
import { Assessment } from '@mui/icons-material';
import { useI18n } from '../../i18n/useI18n';

const ReportsPage: React.FC = () => {
  const { t } = useI18n();

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          {t('reports.title')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<Assessment />}
          size="large"
        >
          {t('reports.generateReport')}
        </Button>
      </Box>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('common.loading')}...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('reports.title')} - {t('common.loading')}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ReportsPage;
