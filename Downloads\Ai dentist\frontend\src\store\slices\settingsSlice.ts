/**
 * Settings slice for Redux store
 * شريحة الإعدادات لمتجر Redux
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Types
interface NotificationSettings {
  email: boolean;
  sms: boolean;
  push: boolean;
  urgentCases: boolean;
  followUpReminders: boolean;
  systemUpdates: boolean;
}

interface PrivacySettings {
  dataSharing: boolean;
  analyticsTracking: boolean;
  researchParticipation: boolean;
  marketingCommunications: boolean;
}

interface DisplaySettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'ar' | 'fr' | 'en';
  dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
  timezone: string;
  fontSize: 'small' | 'medium' | 'large';
  highContrast: boolean;
}

interface AnalysisSettings {
  confidenceThreshold: number;
  autoReview: boolean;
  showHeatmaps: boolean;
  defaultLanguage: 'ar' | 'fr' | 'en';
  saveOriginalImages: boolean;
  compressionLevel: 'low' | 'medium' | 'high';
}

interface SecuritySettings {
  twoFactorAuth: boolean;
  sessionTimeout: number; // in minutes
  passwordExpiry: number; // in days
  loginNotifications: boolean;
  deviceTracking: boolean;
}

interface BackupSettings {
  autoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  backupLocation: 'local' | 'cloud' | 'both';
  retentionPeriod: number; // in days
}

interface SettingsState {
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  display: DisplaySettings;
  analysis: AnalysisSettings;
  security: SecuritySettings;
  backup: BackupSettings;
  isLoading: boolean;
  error: string | null;
  lastSaved: string | null;
}

// Initial state
const initialState: SettingsState = {
  notifications: {
    email: true,
    sms: false,
    push: true,
    urgentCases: true,
    followUpReminders: true,
    systemUpdates: false,
  },
  privacy: {
    dataSharing: false,
    analyticsTracking: true,
    researchParticipation: false,
    marketingCommunications: false,
  },
  display: {
    theme: 'light',
    language: 'ar',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    timezone: 'Africa/Tunis',
    fontSize: 'medium',
    highContrast: false,
  },
  analysis: {
    confidenceThreshold: 0.7,
    autoReview: false,
    showHeatmaps: true,
    defaultLanguage: 'ar',
    saveOriginalImages: true,
    compressionLevel: 'medium',
  },
  security: {
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginNotifications: true,
    deviceTracking: true,
  },
  backup: {
    autoBackup: true,
    backupFrequency: 'daily',
    backupLocation: 'local',
    retentionPeriod: 30,
  },
  isLoading: false,
  error: null,
  lastSaved: null,
};

// Settings slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // Notification settings
    updateNotificationSettings: (state, action: PayloadAction<Partial<NotificationSettings>>) => {
      state.notifications = { ...state.notifications, ...action.payload };
    },
    
    // Privacy settings
    updatePrivacySettings: (state, action: PayloadAction<Partial<PrivacySettings>>) => {
      state.privacy = { ...state.privacy, ...action.payload };
    },
    
    // Display settings
    updateDisplaySettings: (state, action: PayloadAction<Partial<DisplaySettings>>) => {
      state.display = { ...state.display, ...action.payload };
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {
      state.display.theme = action.payload;
    },
    setLanguage: (state, action: PayloadAction<'ar' | 'fr' | 'en'>) => {
      state.display.language = action.payload;
    },
    setFontSize: (state, action: PayloadAction<'small' | 'medium' | 'large'>) => {
      state.display.fontSize = action.payload;
    },
    toggleHighContrast: (state) => {
      state.display.highContrast = !state.display.highContrast;
    },
    
    // Analysis settings
    updateAnalysisSettings: (state, action: PayloadAction<Partial<AnalysisSettings>>) => {
      state.analysis = { ...state.analysis, ...action.payload };
    },
    setConfidenceThreshold: (state, action: PayloadAction<number>) => {
      state.analysis.confidenceThreshold = action.payload;
    },
    toggleAutoReview: (state) => {
      state.analysis.autoReview = !state.analysis.autoReview;
    },
    toggleShowHeatmaps: (state) => {
      state.analysis.showHeatmaps = !state.analysis.showHeatmaps;
    },
    
    // Security settings
    updateSecuritySettings: (state, action: PayloadAction<Partial<SecuritySettings>>) => {
      state.security = { ...state.security, ...action.payload };
    },
    toggleTwoFactorAuth: (state) => {
      state.security.twoFactorAuth = !state.security.twoFactorAuth;
    },
    setSessionTimeout: (state, action: PayloadAction<number>) => {
      state.security.sessionTimeout = action.payload;
    },
    
    // Backup settings
    updateBackupSettings: (state, action: PayloadAction<Partial<BackupSettings>>) => {
      state.backup = { ...state.backup, ...action.payload };
    },
    toggleAutoBackup: (state) => {
      state.backup.autoBackup = !state.backup.autoBackup;
    },
    
    // General
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setLastSaved: (state, action: PayloadAction<string>) => {
      state.lastSaved = action.payload;
    },
    
    // Reset to defaults
    resetNotificationSettings: (state) => {
      state.notifications = initialState.notifications;
    },
    resetPrivacySettings: (state) => {
      state.privacy = initialState.privacy;
    },
    resetDisplaySettings: (state) => {
      state.display = initialState.display;
    },
    resetAnalysisSettings: (state) => {
      state.analysis = initialState.analysis;
    },
    resetSecuritySettings: (state) => {
      state.security = initialState.security;
    },
    resetBackupSettings: (state) => {
      state.backup = initialState.backup;
    },
    resetAllSettings: (state) => {
      return { ...initialState, lastSaved: null };
    },
    
    // Import/Export
    importSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      const { isLoading, error, lastSaved, ...settings } = action.payload;
      return { ...state, ...settings };
    },
    
    // Bulk update
    updateAllSettings: (state, action: PayloadAction<{
      notifications?: Partial<NotificationSettings>;
      privacy?: Partial<PrivacySettings>;
      display?: Partial<DisplaySettings>;
      analysis?: Partial<AnalysisSettings>;
      security?: Partial<SecuritySettings>;
      backup?: Partial<BackupSettings>;
    }>) => {
      const { notifications, privacy, display, analysis, security, backup } = action.payload;
      
      if (notifications) {
        state.notifications = { ...state.notifications, ...notifications };
      }
      if (privacy) {
        state.privacy = { ...state.privacy, ...privacy };
      }
      if (display) {
        state.display = { ...state.display, ...display };
      }
      if (analysis) {
        state.analysis = { ...state.analysis, ...analysis };
      }
      if (security) {
        state.security = { ...state.security, ...security };
      }
      if (backup) {
        state.backup = { ...state.backup, ...backup };
      }
      
      state.lastSaved = new Date().toISOString();
    },
  },
});

export const {
  // Notification settings
  updateNotificationSettings,
  
  // Privacy settings
  updatePrivacySettings,
  
  // Display settings
  updateDisplaySettings,
  setTheme,
  setLanguage,
  setFontSize,
  toggleHighContrast,
  
  // Analysis settings
  updateAnalysisSettings,
  setConfidenceThreshold,
  toggleAutoReview,
  toggleShowHeatmaps,
  
  // Security settings
  updateSecuritySettings,
  toggleTwoFactorAuth,
  setSessionTimeout,
  
  // Backup settings
  updateBackupSettings,
  toggleAutoBackup,
  
  // General
  setLoading,
  setError,
  clearError,
  setLastSaved,
  
  // Reset to defaults
  resetNotificationSettings,
  resetPrivacySettings,
  resetDisplaySettings,
  resetAnalysisSettings,
  resetSecuritySettings,
  resetBackupSettings,
  resetAllSettings,
  
  // Import/Export
  importSettings,
  
  // Bulk update
  updateAllSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;
