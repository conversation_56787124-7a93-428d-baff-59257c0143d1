"""
Pydantic schemas for X-ray analysis
مخططات Pydantic لتحليل الأشعة السينية
"""

from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime


class PathologyPrediction(BaseModel):
    """Schema for individual pathology prediction"""
    class_name: str
    name: str
    confidence: float
    severity: str
    severity_localized: str
    recommendations: List[str]
    urgent: bool


class XrayAnalysisBase(BaseModel):
    """Base schema for X-ray analysis"""
    image_format: str
    image_size: int
    image_metadata: Optional[Dict[str, Any]] = {}
    ai_predictions: List[PathologyPrediction] = []
    confidence_scores: Optional[Dict[str, float]] = {}
    detected_pathologies: List[PathologyPrediction] = []


class XrayAnalysisCreate(XrayAnalysisBase):
    """Schema for creating X-ray analysis"""
    patient_id: int
    dentist_review: Optional[str] = None
    dentist_diagnosis: Optional[str] = None
    recommended_treatment: Optional[str] = None
    treatment_priority: Optional[str] = "medium"
    follow_up_required: bool = False
    follow_up_date: Optional[datetime] = None


class XrayAnalysisUpdate(BaseModel):
    """Schema for updating X-ray analysis review"""
    dentist_review: Optional[str] = None
    dentist_diagnosis: Optional[str] = None
    recommended_treatment: Optional[str] = None
    treatment_priority: Optional[str] = None
    follow_up_required: Optional[bool] = None
    follow_up_date: Optional[datetime] = None
    
    @validator('treatment_priority')
    def validate_priority(cls, v):
        if v and v not in ['low', 'medium', 'high', 'critical']:
            raise ValueError('Priority must be one of: low, medium, high, critical')
        return v


class XrayAnalysisResponse(XrayAnalysisBase):
    """Schema for X-ray analysis response"""
    id: int
    uuid: str
    patient_id: int
    dentist_id: int
    image_path: str
    heatmap_path: Optional[str] = None
    dentist_review: Optional[str] = None
    dentist_diagnosis: Optional[str] = None
    is_reviewed: bool
    review_date: Optional[datetime] = None
    recommended_treatment: Optional[str] = None
    treatment_priority: str
    follow_up_required: bool
    follow_up_date: Optional[datetime] = None
    model_version: str
    processing_time: float
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class AnalysisStatistics(BaseModel):
    """Schema for analysis statistics"""
    total_analyses: int
    pathologies_detected: Dict[str, int]
    severity_distribution: Dict[str, int]
    average_processing_time: float
    reviewed_percentage: float
    urgent_cases: int


class BatchAnalysisRequest(BaseModel):
    """Schema for batch analysis request"""
    patient_ids: List[int]
    language: str = "ar"
    
    @validator('patient_ids')
    def validate_patient_ids(cls, v):
        if len(v) > 10:  # Limit batch size
            raise ValueError('Maximum 10 patients per batch')
        return v


class ComparisonAnalysis(BaseModel):
    """Schema for temporal comparison analysis"""
    current_analysis_id: int
    previous_analysis_id: int
    comparison_results: Dict[str, Any]
    progression_detected: bool
    improvement_detected: bool
    recommendations: List[str]
