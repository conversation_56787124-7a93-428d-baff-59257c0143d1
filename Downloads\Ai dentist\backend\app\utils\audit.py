"""
Audit logging utilities for INPDP compliance
أدوات سجل التدقيق للامتثال لقوانين حماية البيانات
"""

from sqlalchemy.orm import Session
from fastapi import Request
from typing import Optional, Dict, Any
from datetime import datetime
import json

from backend.database.models import AuditLog


async def log_user_action(
    db: Session,
    user_id: int,
    action: str,
    resource_type: str,
    resource_id: str,
    details: Optional[Dict[str, Any]] = None,
    request: Optional[Request] = None
):
    """
    Log user action for audit trail
    تسجيل إجراء المستخدم لمسار التدقيق
    """
    try:
        # Extract request information if available
        ip_address = None
        user_agent = None

        if request:
            ip_address = get_client_ip(request)
            user_agent = request.headers.get("user-agent", "")

        # Create audit log entry
        audit_entry = AuditLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details or {},
            timestamp=datetime.utcnow()
        )

        db.add(audit_entry)
        db.commit()

    except Exception as e:
        # Log error but don't fail the main operation
        print(f"Error logging audit entry: {e}")


def get_client_ip(request: Request) -> str:
    """
    Extract client IP address from request
    استخراج عنوان IP للعميل من الطلب
    """
    # Check for forwarded headers (proxy/load balancer)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    # Fallback to direct connection
    return request.client.host if request.client else "unknown"


class AuditActions:
    """
    Constants for audit action types
    ثوابت لأنواع إجراءات التدقيق
    """
    # Authentication
    LOGIN = "login"
    LOGOUT = "logout"
    FAILED_LOGIN = "failed_login"
    PASSWORD_CHANGE = "password_change"

    # User Management
    USER_CREATE = "user_create"
    USER_UPDATE = "user_update"
    USER_DELETE = "user_delete"
    USER_VIEW = "user_view"

    # Patient Management
    PATIENT_CREATE = "patient_create"
    PATIENT_UPDATE = "patient_update"
    PATIENT_DELETE = "patient_delete"
    PATIENT_VIEW = "patient_view"
    PATIENT_SEARCH = "patient_search"

    # Medical Records
    XRAY_UPLOAD = "xray_upload"
    XRAY_ANALYZE = "xray_analyze"
    XRAY_VIEW = "xray_view"
    XRAY_DELETE = "xray_delete"
    DIAGNOSIS_UPDATE = "diagnosis_update"

    # Appointments
    APPOINTMENT_CREATE = "appointment_create"
    APPOINTMENT_UPDATE = "appointment_update"
    APPOINTMENT_DELETE = "appointment_delete"
    APPOINTMENT_VIEW = "appointment_view"

    # Telemedicine
    CONSULTATION_START = "consultation_start"
    CONSULTATION_END = "consultation_end"
    CONSULTATION_VIEW = "consultation_view"

    # Reports
    REPORT_GENERATE = "report_generate"
    REPORT_VIEW = "report_view"
    REPORT_EXPORT = "report_export"

    # System
    SYSTEM_BACKUP = "system_backup"
    SYSTEM_RESTORE = "system_restore"
    SETTINGS_UPDATE = "settings_update"

    # Data Privacy (INPDP)
    DATA_EXPORT = "data_export"
    DATA_ANONYMIZE = "data_anonymize"
    CONSENT_UPDATE = "consent_update"
    DATA_DELETION = "data_deletion"


class AuditResourceTypes:
    """
    Constants for audit resource types
    ثوابت لأنواع موارد التدقيق
    """
    USER = "user"
    PATIENT = "patient"
    XRAY = "xray"
    APPOINTMENT = "appointment"
    CONSULTATION = "consultation"
    REPORT = "report"
    SYSTEM = "system"
    AUTH = "auth"


def create_audit_details(
    old_data: Optional[Dict] = None,
    new_data: Optional[Dict] = None,
    additional_info: Optional[Dict] = None
) -> Dict[str, Any]:
    """
    Create structured audit details
    إنشاء تفاصيل تدقيق منظمة
    """
    details = {}

    if old_data:
        details["old_data"] = sanitize_audit_data(old_data)

    if new_data:
        details["new_data"] = sanitize_audit_data(new_data)

    if additional_info:
        details.update(additional_info)

    details["timestamp"] = datetime.utcnow().isoformat()

    return details


def sanitize_audit_data(data: Dict) -> Dict:
    """
    Remove sensitive information from audit data
    إزالة المعلومات الحساسة من بيانات التدقيق
    """
    sensitive_fields = [
        "password", "hashed_password", "token", "secret",
        "credit_card", "ssn", "national_id"
    ]

    sanitized = {}

    for key, value in data.items():
        if key.lower() in sensitive_fields:
            sanitized[key] = "[REDACTED]"
        elif isinstance(value, dict):
            sanitized[key] = sanitize_audit_data(value)
        else:
            sanitized[key] = value

    return sanitized


async def get_audit_trail(
    db: Session,
    user_id: Optional[int] = None,
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None,
    action: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: int = 100
) -> list:
    """
    Retrieve audit trail with filters
    استرداد مسار التدقيق مع المرشحات
    """
    query = db.query(AuditLog)

    if user_id:
        query = query.filter(AuditLog.user_id == user_id)

    if resource_type:
        query = query.filter(AuditLog.resource_type == resource_type)

    if resource_id:
        query = query.filter(AuditLog.resource_id == resource_id)

    if action:
        query = query.filter(AuditLog.action == action)

    if start_date:
        query = query.filter(AuditLog.timestamp >= start_date)

    if end_date:
        query = query.filter(AuditLog.timestamp <= end_date)

    # Order by timestamp descending
    query = query.order_by(AuditLog.timestamp.desc())

    # Apply limit
    query = query.limit(limit)

    return query.all()


def generate_audit_report(
    db: Session,
    start_date: datetime,
    end_date: datetime,
    user_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Generate audit report for compliance
    إنتاج تقرير تدقيق للامتثال
    """
    audit_logs = get_audit_trail(
        db=db,
        user_id=user_id,
        start_date=start_date,
        end_date=end_date,
        limit=10000  # Large limit for reports
    )

    # Aggregate statistics
    action_counts = {}
    resource_counts = {}
    daily_activity = {}

    for log in audit_logs:
        # Count actions
        action_counts[log.action] = action_counts.get(log.action, 0) + 1

        # Count resources
        resource_counts[log.resource_type] = resource_counts.get(log.resource_type, 0) + 1

        # Daily activity
        day = log.timestamp.date().isoformat()
        daily_activity[day] = daily_activity.get(day, 0) + 1

    return {
        "period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        },
        "total_events": len(audit_logs),
        "action_summary": action_counts,
        "resource_summary": resource_counts,
        "daily_activity": daily_activity,
        "generated_at": datetime.utcnow().isoformat()
    }


async def log_analysis_activity(
    user_id: int,
    patient_id: int,
    analysis_id: Optional[int],
    action: str,
    details: Optional[Dict[str, Any]] = None
):
    """
    Log analysis-related activity for audit trail
    تسجيل النشاط المتعلق بالتحليل لمسار التدقيق
    """
    # This is a simplified version for development
    # In production, this would use the database session
    print(f"AUDIT LOG: User {user_id} performed {action} on patient {patient_id}")
    if details:
        print(f"Details: {details}")

    # TODO: Implement proper database logging when database is ready
    pass
