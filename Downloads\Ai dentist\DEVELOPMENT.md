# 🚀 Development Guide - دليل التطوير

## Overview / نظرة عامة

This guide will help you set up and develop the AI Dental Assistant application locally.

سيساعدك هذا الدليل في إعداد وتطوير تطبيق مساعد طبيب الأسنان الذكي محلياً.

## 📋 Prerequisites / المتطلبات المسبقة

### Required Software / البرامج المطلوبة
- **Docker** (v20.10+) & **Docker Compose** (v2.0+)
- **Python** (3.9+) for backend development
- **Node.js** (16+) & **npm** for frontend development
- **Git** for version control

### Optional Tools / أدوات اختيارية
- **PostgreSQL** (13+) for local database development
- **Redis** (6+) for local caching
- **VS Code** with Python and React extensions

## 🛠️ Quick Start / البدء السريع

### 1. Clone the Repository / استنساخ المستودع
```bash
git clone <repository-url>
cd dental-ai-app
```

### 2. Environment Setup / إعداد البيئة
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

### 3. Start with Docker / البدء باستخدام Docker
```bash
# Make startup script executable
chmod +x start.sh

# Run the startup script
./start.sh
```

### 4. Access the Application / الوصول إلى التطبيق
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Database**: localhost:5432 (dental_ai_db)
- **Redis**: localhost:6379

## 🏗️ Development Setup / إعداد التطوير

### Backend Development / تطوير الخادم الخلفي

1. **Setup Python Environment**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Database Setup**
```bash
# Start PostgreSQL and Redis
docker-compose up -d postgres redis

# Run database migrations
alembic upgrade head
```

3. **Start Development Server**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Development / تطوير الواجهة الأمامية

1. **Setup Node.js Environment**
```bash
cd frontend
npm install
```

2. **Start Development Server**
```bash
npm start
```

The frontend will be available at http://localhost:3000 with hot reload.

### AI Models Development / تطوير نماذج الذكاء الاصطناعي

1. **Setup AI Environment**
```bash
cd ai-models
pip install torch torchvision opencv-python pydicom
```

2. **Train Models**
```bash
python training/train_model.py
```

## 📁 Project Structure / هيكل المشروع

```
dental-ai-app/
├── backend/                 # FastAPI backend
│   ├── app/                # Application code
│   │   ├── routers/        # API endpoints
│   │   ├── models/         # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   ├── utils/          # Utility functions
│   │   └── middleware/     # Custom middleware
│   ├── database/           # Database configuration
│   └── tests/              # Backend tests
├── frontend/               # React frontend
│   ├── src/               # Source code
│   │   ├── components/    # React components
│   │   ├── pages/         # Page components
│   │   ├── store/         # Redux store
│   │   ├── i18n/          # Internationalization
│   │   └── utils/         # Utility functions
│   └── public/            # Static assets
├── ai-models/             # AI/ML models
│   ├── training/          # Model training
│   ├── inference/         # Model inference
│   └── data/             # Training data
├── docker/               # Docker configurations
├── docs/                 # Documentation
└── tests/               # Integration tests
```

## 🧪 Testing / الاختبار

### Backend Tests / اختبارات الخادم الخلفي
```bash
cd backend
pytest
pytest --cov=app tests/  # With coverage
```

### Frontend Tests / اختبارات الواجهة الأمامية
```bash
cd frontend
npm test
npm run test:coverage
```

### Integration Tests / اختبارات التكامل
```bash
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 🔧 Development Tools / أدوات التطوير

### Code Formatting / تنسيق الكود
```bash
# Backend (Python)
cd backend
black .
isort .
flake8 .

# Frontend (TypeScript/JavaScript)
cd frontend
npm run format
npm run lint
```

### Database Management / إدارة قاعدة البيانات
```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## 🌐 Internationalization / التدويل

### Adding New Languages / إضافة لغات جديدة
1. Add language to `frontend/src/i18n/locales/`
2. Update language configuration in `config.py`
3. Add translations for all text strings

### Arabic RTL Support / دعم العربية من اليمين إلى اليسار
- The application automatically switches direction based on language
- Use Material-UI's RTL theme support
- Test all components in both LTR and RTL modes

## 🔒 Security Considerations / اعتبارات الأمان

### INPDP Compliance / الامتثال لقوانين حماية البيانات
- All patient data is encrypted
- Audit logs are maintained for all actions
- Data retention policies are enforced
- Consent management is implemented

### Authentication / المصادقة
- JWT tokens with expiration
- Password hashing with bcrypt
- Role-based access control
- Session management

## 📊 Monitoring / المراقبة

### Logs / السجلات
- Application logs: `logs/dental_ai.log`
- Access logs: Available in Docker containers
- Error tracking: Integrated with application

### Health Checks / فحوصات الصحة
- Backend: `GET /health`
- Database: Automatic health checks
- Redis: Connection monitoring

## 🚀 Deployment / النشر

### Production Deployment / النشر في الإنتاج
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy with production configuration
docker-compose -f docker-compose.prod.yml up -d
```

### Environment Variables / متغيرات البيئة
- Update `.env` file for production
- Use secure secrets management
- Configure SSL certificates
- Set up backup strategies

## 🤝 Contributing / المساهمة

### Code Style / أسلوب الكود
- Follow PEP 8 for Python code
- Use TypeScript for frontend development
- Write comprehensive tests
- Document all functions and classes

### Pull Request Process / عملية طلب السحب
1. Create feature branch from `main`
2. Implement changes with tests
3. Update documentation
4. Submit pull request with description
5. Code review and approval
6. Merge to main branch

## 📞 Support / الدعم

### Getting Help / الحصول على المساعدة
- Check documentation first
- Search existing issues
- Create new issue with details
- Contact development team

### Common Issues / المشاكل الشائعة
- **Port conflicts**: Change ports in docker-compose.yml
- **Database connection**: Check PostgreSQL container status
- **Permission errors**: Ensure proper file permissions
- **Memory issues**: Increase Docker memory allocation

---

**Happy Coding! / برمجة سعيدة!** 🚀
