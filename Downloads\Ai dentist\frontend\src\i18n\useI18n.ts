/**
 * Custom hook for internationalization
 * خطاف مخصص للتدويل
 */

import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

// Re-export the hook from I18nProvider for consistency
export { useI18n } from './I18nProvider';

// Additional utility hook that combines react-i18next with our custom context
export const useTranslationWithDirection = () => {
  const { t, i18n } = useTranslation();
  
  const isRTL = i18n.language === 'ar';
  const direction = isRTL ? 'rtl' : 'ltr';
  
  return {
    t,
    i18n,
    isRTL,
    direction,
    currentLanguage: i18n.language,
  };
};
