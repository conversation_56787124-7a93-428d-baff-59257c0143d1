# Environment variables for AI Dental Assistant
# متغيرات البيئة لمساعد طبيب الأسنان الذكي

# Application
APP_NAME="AI Dental Assistant"
VERSION="1.0.0"
ENVIRONMENT="development"
DEBUG=true

# Security
SECRET_KEY="your-super-secret-key-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database
DATABASE_URL="postgresql://dental_user:dental_pass@localhost:5432/dental_ai_db"
DATABASE_ECHO=false

# Redis
REDIS_URL="redis://localhost:6379/0"

# CORS
ALLOWED_ORIGINS="http://localhost:3000,http://localhost:8080"

# File Upload
MAX_FILE_SIZE=52428800  # 50MB
UPLOAD_DIR="uploads"

# AI Model
MODEL_PATH="models/dental_resnet.pth"
MODEL_DEVICE="cpu"
CONFIDENCE_THRESHOLD=0.7

# Email (Optional)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USERNAME=""
SMTP_PASSWORD=""
SMTP_TLS=true

# Logging
LOG_LEVEL="INFO"
LOG_FILE="logs/dental_ai.log"

# INPDP Compliance
DATA_RETENTION_DAYS=2555
ANONYMIZATION_ENABLED=true
AUDIT_LOG_ENABLED=true

# Tunisian Specific
CNAM_INTEGRATION_ENABLED=false
TUNISIAN_ID_VALIDATION=true
ARABIC_RTL_SUPPORT=true
