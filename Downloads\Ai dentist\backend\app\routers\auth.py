"""
Authentication router for AI Dental Assistant
موجه المصادقة لمساعد طبيب الأسنان الذكي
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>P<PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Optional
import jwt
from passlib.context import CryptContext

from backend.database.connection import get_db
from backend.database.models import User, AuditLog
from backend.app.config import settings
from backend.app.schemas.auth import UserCreate, UserResponse, Token, UserLogin, UserUpdate, PasswordChange
from backend.app.utils.security import create_access_token, verify_password, get_password_hash
from backend.app.utils.audit import log_user_action

router = APIRouter()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    """
    Register a new dentist or staff member
    تسجيل طبيب أسنان أو موظف جديد
    """
    # Check if user already exists
    existing_user = db.query(User).filter(
        (User.email == user_data.email) |
        (User.username == user_data.username)
    ).first()

    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "User already exists",
                "message_ar": "المستخدم موجود بالفعل"
            }
        )

    # Check license number uniqueness for dentists
    if user_data.role == "dentist" and user_data.license_number:
        existing_license = db.query(User).filter(
            User.license_number == user_data.license_number
        ).first()

        if existing_license:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "License number already registered",
                    "message_ar": "رقم الترخيص مسجل بالفعل"
                }
            )

    # Create new user
    hashed_password = get_password_hash(user_data.password)

    new_user = User(
        email=user_data.email,
        username=user_data.username,
        hashed_password=hashed_password,
        first_name=user_data.first_name,
        last_name=user_data.last_name,
        first_name_ar=user_data.first_name_ar,
        last_name_ar=user_data.last_name_ar,
        license_number=user_data.license_number,
        specialization=user_data.specialization,
        clinic_name=user_data.clinic_name,
        clinic_address=user_data.clinic_address,
        phone=user_data.phone,
        role=user_data.role,
        preferred_language=user_data.preferred_language or "ar"
    )

    db.add(new_user)
    db.commit()
    db.refresh(new_user)

    # Log registration
    await log_user_action(
        db=db,
        user_id=new_user.id,
        action="user_registration",
        resource_type="user",
        resource_id=str(new_user.id),
        details={"role": new_user.role, "email": new_user.email}
    )

    return UserResponse.from_orm(new_user)


@router.post("/login", response_model=Token)
async def login_user(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    Authenticate user and return access token
    مصادقة المستخدم وإرجاع رمز الوصول
    """
    # Find user by username or email
    user = db.query(User).filter(
        (User.username == form_data.username) |
        (User.email == form_data.username)
    ).first()

    if not user or not verify_password(form_data.password, user.hashed_password):
        # Log failed login attempt
        if user:
            await log_user_action(
                db=db,
                user_id=user.id,
                action="failed_login",
                resource_type="auth",
                resource_id=str(user.id),
                details={"reason": "invalid_password"}
            )

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "Incorrect username or password",
                "message_ar": "اسم المستخدم أو كلمة المرور غير صحيحة"
            },
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Inactive user account",
                "message_ar": "حساب المستخدم غير نشط"
            }
        )

    # Update last login
    user.last_login = datetime.utcnow()
    db.commit()

    # Create access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id, "role": user.role},
        expires_delta=access_token_expires
    )

    # Log successful login
    await log_user_action(
        db=db,
        user_id=user.id,
        action="successful_login",
        resource_type="auth",
        resource_id=str(user.id),
        details={"login_time": datetime.utcnow().isoformat()}
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": UserResponse.from_orm(user)
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get current authenticated user information
    الحصول على معلومات المستخدم المصادق عليه حالياً
    """
    return UserResponse.from_orm(current_user)


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user profile
    تحديث ملف المستخدم الحالي
    """
    # Update allowed fields
    update_data = user_update.dict(exclude_unset=True)

    for field, value in update_data.items():
        if hasattr(current_user, field):
            setattr(current_user, field, value)

    current_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(current_user)

    # Log profile update
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action="profile_update",
        resource_type="user",
        resource_id=str(current_user.id),
        details={"updated_fields": list(update_data.keys())}
    )

    return UserResponse.from_orm(current_user)


@router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Change user password
    تغيير كلمة مرور المستخدم
    """
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Current password is incorrect",
                "message_ar": "كلمة المرور الحالية غير صحيحة"
            }
        )

    # Update password
    current_user.hashed_password = get_password_hash(password_data.new_password)
    current_user.updated_at = datetime.utcnow()
    db.commit()

    # Log password change
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action="password_change",
        resource_type="user",
        resource_id=str(current_user.id),
        details={"timestamp": datetime.utcnow().isoformat()}
    )

    return {
        "message": "Password changed successfully",
        "message_ar": "تم تغيير كلمة المرور بنجاح"
    }


@router.post("/logout")
async def logout_user(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Logout user (invalidate token on client side)
    تسجيل خروج المستخدم
    """
    # Log logout
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action="logout",
        resource_type="auth",
        resource_id=str(current_user.id),
        details={"logout_time": datetime.utcnow().isoformat()}
    )

    return {
        "message": "Logged out successfully",
        "message_ar": "تم تسجيل الخروج بنجاح"
    }


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """Get current user from JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail={
            "message": "Could not validate credentials",
            "message_ar": "لا يمكن التحقق من بيانات الاعتماد"
        },
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        username: str = payload.get("sub")
        user_id: int = payload.get("user_id")

        if username is None or user_id is None:
            raise credentials_exception

    except jwt.PyJWTError:
        raise credentials_exception

    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise credentials_exception

    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Inactive user",
                "message_ar": "مستخدم غير نشط"
            }
        )
    return current_user
