"""
Database models for AI Dental Assistant
نماذج قاعدة البيانات لمساعد طبيب الأسنان الذكي
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Float, ForeignKey, JSON, LargeBinary
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import uuid

Base = declarative_base()


class User(Base):
    """User model for dentists and staff / نموذج المستخدم للأطباء والموظفين"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Personal Information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    first_name_ar = Column(String(100))  # Arabic name
    last_name_ar = Column(String(100))   # Arabic name
    
    # Professional Information
    license_number = Column(String(50), unique=True)  # Tunisian dental license
    specialization = Column(String(100))
    clinic_name = Column(String(200))
    clinic_address = Column(Text)
    phone = Column(String(20))
    
    # System Information
    role = Column(String(50), default="dentist")  # dentist, assistant, admin
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    preferred_language = Column(String(5), default="ar")  # ar, fr, en
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # Relationships
    patients = relationship("Patient", back_populates="dentist")
    analyses = relationship("XrayAnalysis", back_populates="dentist")
    consultations = relationship("TelemedicineConsultation", back_populates="dentist")


class Patient(Base):
    """Patient model with INPDP compliance / نموذج المريض مع الامتثال لقوانين حماية البيانات"""
    __tablename__ = "patients"
    
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    
    # Personal Information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    first_name_ar = Column(String(100))
    last_name_ar = Column(String(100))
    date_of_birth = Column(DateTime)
    gender = Column(String(10))  # male, female, other
    
    # Tunisian Identification
    national_id = Column(String(20), unique=True)  # Tunisian national ID
    cnam_number = Column(String(20))  # CNAM social security number
    
    # Contact Information
    phone = Column(String(20))
    email = Column(String(255))
    address = Column(Text)
    city = Column(String(100))
    postal_code = Column(String(10))
    
    # Medical Information
    medical_history = Column(JSON)  # Structured medical history
    allergies = Column(Text)
    current_medications = Column(Text)
    emergency_contact = Column(JSON)
    
    # Consent and Privacy (INPDP Compliance)
    consent_given = Column(Boolean, default=False)
    consent_date = Column(DateTime)
    data_processing_consent = Column(Boolean, default=False)
    research_consent = Column(Boolean, default=False)
    
    # System Information
    dentist_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    is_active = Column(Boolean, default=True)
    preferred_language = Column(String(5), default="ar")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    dentist = relationship("User", back_populates="patients")
    xray_analyses = relationship("XrayAnalysis", back_populates="patient")
    appointments = relationship("Appointment", back_populates="patient")
    consultations = relationship("TelemedicineConsultation", back_populates="patient")


class XrayAnalysis(Base):
    """X-ray analysis results from AI model / نتائج تحليل الأشعة من نموذج الذكاء الاصطناعي"""
    __tablename__ = "xray_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    
    # Image Information
    image_path = Column(String(500), nullable=False)
    image_format = Column(String(10))  # DICOM, JPG, PNG
    image_size = Column(Integer)  # File size in bytes
    image_metadata = Column(JSON)  # DICOM metadata or other image info
    
    # AI Analysis Results
    ai_predictions = Column(JSON, nullable=False)  # AI model predictions
    confidence_scores = Column(JSON)  # Confidence scores for each prediction
    heatmap_path = Column(String(500))  # Path to generated heatmap
    detected_pathologies = Column(JSON)  # Structured pathology data
    
    # Clinical Review
    dentist_review = Column(Text)  # Dentist's review and notes
    dentist_diagnosis = Column(Text)  # Final diagnosis by dentist
    is_reviewed = Column(Boolean, default=False)
    review_date = Column(DateTime)
    
    # Treatment Planning
    recommended_treatment = Column(Text)
    treatment_priority = Column(String(20))  # low, medium, high, critical
    follow_up_required = Column(Boolean, default=False)
    follow_up_date = Column(DateTime)
    
    # System Information
    patient_id = Column(Integer, ForeignKey("patients.id"), nullable=False)
    dentist_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    model_version = Column(String(20))  # AI model version used
    processing_time = Column(Float)  # Time taken for analysis in seconds
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    patient = relationship("Patient", back_populates="xray_analyses")
    dentist = relationship("User", back_populates="analyses")


class Appointment(Base):
    """Appointment scheduling / جدولة المواعيد"""
    __tablename__ = "appointments"
    
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    
    # Appointment Details
    appointment_date = Column(DateTime, nullable=False)
    duration_minutes = Column(Integer, default=30)
    appointment_type = Column(String(50))  # consultation, treatment, follow_up
    status = Column(String(20), default="scheduled")  # scheduled, confirmed, cancelled, completed
    
    # Description
    reason = Column(Text)
    notes = Column(Text)
    
    # Relationships
    patient_id = Column(Integer, ForeignKey("patients.id"), nullable=False)
    dentist_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    patient = relationship("Patient", back_populates="appointments")
    dentist = relationship("User")


class TelemedicineConsultation(Base):
    """Telemedicine consultation records / سجلات الاستشارة الطبية عن بُعد"""
    __tablename__ = "telemedicine_consultations"
    
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, default=lambda: str(uuid.uuid4()))
    
    # Consultation Details
    consultation_date = Column(DateTime, nullable=False)
    duration_minutes = Column(Integer)
    consultation_type = Column(String(50))  # video, audio, chat
    status = Column(String(20))  # scheduled, in_progress, completed, cancelled
    
    # Technical Details
    video_quality = Column(String(20))  # low, medium, high
    connection_issues = Column(Boolean, default=False)
    recording_path = Column(String(500))  # If consultation is recorded
    
    # Medical Content
    chief_complaint = Column(Text)
    consultation_notes = Column(Text)
    recommendations = Column(Text)
    prescription = Column(Text)
    follow_up_required = Column(Boolean, default=False)
    
    # Relationships
    patient_id = Column(Integer, ForeignKey("patients.id"), nullable=False)
    dentist_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    patient = relationship("Patient", back_populates="consultations")
    dentist = relationship("User", back_populates="consultations")


class AuditLog(Base):
    """Audit log for INPDP compliance / سجل التدقيق للامتثال لقوانين حماية البيانات"""
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # User and Action Information
    user_id = Column(Integer, ForeignKey("users.id"))
    action = Column(String(100), nullable=False)  # login, view_patient, update_record, etc.
    resource_type = Column(String(50))  # patient, xray, appointment, etc.
    resource_id = Column(String(50))
    
    # Details
    ip_address = Column(String(45))  # IPv4 or IPv6
    user_agent = Column(String(500))
    details = Column(JSON)  # Additional action details
    
    # Timestamp
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")
