/**
 * Internationalization Provider for AI Dental Assistant
 * مزود التدويل لمساعد طبيب الأسنان الذكي
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import { prefixer } from 'stylis';
import rtlPlugin from 'stylis-plugin-rtl';

// Import translations
import arTranslations from './locales/ar.json';
import frTranslations from './locales/fr.json';
import enTranslations from './locales/en.json';

// Language configuration
const languages = {
  ar: {
    name: 'العربية',
    dir: 'rtl',
    translations: arTranslations,
  },
  fr: {
    name: 'Français',
    dir: 'ltr',
    translations: frTranslations,
  },
  en: {
    name: 'English',
    dir: 'ltr',
    translations: enTranslations,
  },
};

// Initialize i18next
i18n.use(initReactI18next).init({
  resources: {
    ar: { translation: arTranslations },
    fr: { translation: frTranslations },
    en: { translation: enTranslations },
  },
  lng: 'ar', // Default language
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false,
  },
});

// Create RTL cache
const cacheRtl = createCache({
  key: 'muirtl',
  stylisPlugins: [prefixer, rtlPlugin],
});

// Create LTR cache
const cacheLtr = createCache({
  key: 'muiltr',
});

// I18n Context
interface I18nContextType {
  currentLanguage: string;
  direction: 'rtl' | 'ltr';
  changeLanguage: (language: string) => void;
  t: (key: string, options?: any) => string;
  languages: typeof languages;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

// I18n Provider Component
interface I18nProviderProps {
  children: React.ReactNode;
}

export const I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);
  const [direction, setDirection] = useState<'rtl' | 'ltr'>(
    (languages[currentLanguage as keyof typeof languages]?.dir as 'rtl' | 'ltr') || 'rtl'
  );

  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
    setCurrentLanguage(language);
    setDirection((languages[language as keyof typeof languages]?.dir as 'rtl' | 'ltr') || 'ltr');

    // Update document direction
    document.documentElement.dir = languages[language as keyof typeof languages]?.dir || 'ltr';
    document.documentElement.lang = language;

    // Store preference
    localStorage.setItem('preferred-language', language);
  };

  const t = (key: string, options?: any) => {
    return i18n.t(key, options);
  };

  useEffect(() => {
    // Load saved language preference
    const savedLanguage = localStorage.getItem('preferred-language');
    if (savedLanguage && languages[savedLanguage as keyof typeof languages]) {
      changeLanguage(savedLanguage);
    }
  }, []);

  // Create theme based on direction
  const theme = createTheme({
    direction: direction,
    palette: {
      primary: {
        main: '#1976d2',
        light: '#42a5f5',
        dark: '#1565c0',
      },
      secondary: {
        main: '#dc004e',
        light: '#ff5983',
        dark: '#9a0036',
      },
    },
    typography: {
      fontFamily: direction === 'rtl'
        ? ['Cairo', 'Roboto', 'Arial', 'sans-serif'].join(',')
        : ['Roboto', 'Cairo', 'Arial', 'sans-serif'].join(','),
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            direction: direction,
          },
        },
      },
    },
  });

  const contextValue: I18nContextType = {
    currentLanguage,
    direction,
    changeLanguage,
    t: t as (key: string, options?: any) => string,
    languages,
  };

  return (
    <I18nContext.Provider value={contextValue}>
      <CacheProvider value={direction === 'rtl' ? cacheRtl : cacheLtr}>
        <ThemeProvider theme={theme}>
          {children}
        </ThemeProvider>
      </CacheProvider>
    </I18nContext.Provider>
  );
};

// Custom hook to use I18n context
export const useI18n = (): I18nContextType => {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};

export default I18nProvider;
