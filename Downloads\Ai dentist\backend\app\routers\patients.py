"""
Patient management router for AI Dental Assistant
موجه إدارة المرضى لمساعد طبيب الأسنان الذكي
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from backend.database.connection import get_db
from backend.database.models import User, Patient
from backend.app.routers.auth import get_current_active_user
from backend.app.schemas.patients import PatientCreate, PatientResponse, PatientUpdate, PatientSearch
from backend.app.utils.security import validate_tunisian_id, validate_cnam_number, PermissionChecker
from backend.app.utils.audit import log_user_action, AuditActions, AuditResourceTypes, create_audit_details
from backend.app.config import settings

router = APIRouter()


@router.post("/", response_model=PatientResponse, status_code=status.HTTP_201_CREATED)
async def create_patient(
    patient_data: Patient<PERSON>reate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new patient record
    إنشاء سجل مريض جديد
    """
    # Validate Tunisian ID if provided
    if patient_data.national_id and not validate_tunisian_id(patient_data.national_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Invalid Tunisian national ID format",
                "message_ar": "صيغة الهوية الوطنية التونسية غير صالحة"
            }
        )

    # Validate CNAM number if provided
    if patient_data.cnam_number and not validate_cnam_number(patient_data.cnam_number):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Invalid CNAM number format",
                "message_ar": "صيغة رقم الضمان الاجتماعي غير صالحة"
            }
        )

    # Check for existing patient with same national ID
    if patient_data.national_id:
        existing_patient = db.query(Patient).filter(
            Patient.national_id == patient_data.national_id
        ).first()

        if existing_patient:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Patient with this national ID already exists",
                    "message_ar": "مريض بهذه الهوية الوطنية موجود بالفعل"
                }
            )

    # Create new patient
    new_patient = Patient(
        first_name=patient_data.first_name,
        last_name=patient_data.last_name,
        first_name_ar=patient_data.first_name_ar,
        last_name_ar=patient_data.last_name_ar,
        date_of_birth=patient_data.date_of_birth,
        gender=patient_data.gender,
        national_id=patient_data.national_id,
        cnam_number=patient_data.cnam_number,
        phone=patient_data.phone,
        email=patient_data.email,
        address=patient_data.address,
        city=patient_data.city,
        postal_code=patient_data.postal_code,
        medical_history=patient_data.medical_history or {},
        allergies=patient_data.allergies,
        current_medications=patient_data.current_medications,
        emergency_contact=patient_data.emergency_contact or {},
        consent_given=patient_data.consent_given,
        consent_date=datetime.utcnow() if patient_data.consent_given else None,
        data_processing_consent=patient_data.data_processing_consent,
        research_consent=patient_data.research_consent,
        dentist_id=current_user.id,
        preferred_language=patient_data.preferred_language or "ar"
    )

    db.add(new_patient)
    db.commit()
    db.refresh(new_patient)

    # Log patient creation
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action=AuditActions.PATIENT_CREATE,
        resource_type=AuditResourceTypes.PATIENT,
        resource_id=str(new_patient.id),
        details=create_audit_details(
            new_data={
                "patient_name": f"{new_patient.first_name} {new_patient.last_name}",
                "national_id": new_patient.national_id,
                "consent_given": new_patient.consent_given
            }
        )
    )

    return PatientResponse.from_orm(new_patient)


@router.get("/", response_model=List[PatientResponse])
async def get_patients(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get list of patients for current dentist
    الحصول على قائمة المرضى لطبيب الأسنان الحالي
    """
    query = db.query(Patient)

    # Filter by dentist (unless admin)
    if current_user.role != "admin":
        query = query.filter(Patient.dentist_id == current_user.id)

    # Apply search filter
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (Patient.first_name.ilike(search_term)) |
            (Patient.last_name.ilike(search_term)) |
            (Patient.first_name_ar.ilike(search_term)) |
            (Patient.last_name_ar.ilike(search_term)) |
            (Patient.national_id.ilike(search_term)) |
            (Patient.phone.ilike(search_term))
        )

    # Apply pagination
    patients = query.order_by(Patient.created_at.desc()).offset(skip).limit(limit).all()

    # Log search action
    if search:
        await log_user_action(
            db=db,
            user_id=current_user.id,
            action=AuditActions.PATIENT_SEARCH,
            resource_type=AuditResourceTypes.PATIENT,
            resource_id="search",
            details={"search_term": search, "results_count": len(patients)}
        )

    return [PatientResponse.from_orm(patient) for patient in patients]


@router.get("/{patient_id}", response_model=PatientResponse)
async def get_patient(
    patient_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get specific patient by ID
    الحصول على مريض محدد بالمعرف
    """
    patient = db.query(Patient).filter(Patient.id == patient_id).first()

    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Patient not found",
                "message_ar": "المريض غير موجود"
            }
        )

    # Check access permissions
    if not PermissionChecker.can_access_patient(
        current_user.role, current_user.id, patient.dentist_id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Access denied to patient data",
                "message_ar": "تم رفض الوصول إلى بيانات المريض"
            }
        )

    # Log patient view
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action=AuditActions.PATIENT_VIEW,
        resource_type=AuditResourceTypes.PATIENT,
        resource_id=str(patient_id)
    )

    return PatientResponse.from_orm(patient)


@router.put("/{patient_id}", response_model=PatientResponse)
async def update_patient(
    patient_id: int,
    patient_update: PatientUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update patient information
    تحديث معلومات المريض
    """
    patient = db.query(Patient).filter(Patient.id == patient_id).first()

    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Patient not found",
                "message_ar": "المريض غير موجود"
            }
        )

    # Check modification permissions
    if not PermissionChecker.can_modify_patient(
        current_user.role, current_user.id, patient.dentist_id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Insufficient permissions to modify patient data",
                "message_ar": "صلاحيات غير كافية لتعديل بيانات المريض"
            }
        )

    # Store old data for audit
    old_data = {
        "first_name": patient.first_name,
        "last_name": patient.last_name,
        "phone": patient.phone,
        "email": patient.email,
        "address": patient.address
    }

    # Update patient data
    update_data = patient_update.dict(exclude_unset=True)

    # Validate national ID if being updated
    if "national_id" in update_data and update_data["national_id"]:
        if not validate_tunisian_id(update_data["national_id"]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Invalid Tunisian national ID format",
                    "message_ar": "صيغة الهوية الوطنية التونسية غير صالحة"
                }
            )

    # Validate CNAM number if being updated
    if "cnam_number" in update_data and update_data["cnam_number"]:
        if not validate_cnam_number(update_data["cnam_number"]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Invalid CNAM number format",
                    "message_ar": "صيغة رقم الضمان الاجتماعي غير صالحة"
                }
            )

    # Apply updates
    for field, value in update_data.items():
        if hasattr(patient, field):
            setattr(patient, field, value)

    patient.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(patient)

    # Log patient update
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action=AuditActions.PATIENT_UPDATE,
        resource_type=AuditResourceTypes.PATIENT,
        resource_id=str(patient_id),
        details=create_audit_details(
            old_data=old_data,
            new_data=update_data,
            additional_info={"updated_fields": list(update_data.keys())}
        )
    )

    return PatientResponse.from_orm(patient)


@router.delete("/{patient_id}")
async def delete_patient(
    patient_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete patient record (admin or dentist only)
    حذف سجل المريض (المدير أو طبيب الأسنان فقط)
    """
    patient = db.query(Patient).filter(Patient.id == patient_id).first()

    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Patient not found",
                "message_ar": "المريض غير موجود"
            }
        )

    # Check deletion permissions
    if not PermissionChecker.can_delete_record(current_user.role):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Insufficient permissions to delete patient",
                "message_ar": "صلاحيات غير كافية لحذف المريض"
            }
        )

    # Check if patient belongs to current dentist (unless admin)
    if current_user.role != "admin" and patient.dentist_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Cannot delete patient from another dentist",
                "message_ar": "لا يمكن حذف مريض من طبيب آخر"
            }
        )

    # Store patient info for audit before deletion
    patient_info = {
        "name": f"{patient.first_name} {patient.last_name}",
        "national_id": patient.national_id,
        "created_at": patient.created_at.isoformat()
    }

    # Delete patient
    db.delete(patient)
    db.commit()

    # Log patient deletion
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action=AuditActions.PATIENT_DELETE,
        resource_type=AuditResourceTypes.PATIENT,
        resource_id=str(patient_id),
        details=create_audit_details(
            old_data=patient_info,
            additional_info={"deletion_reason": "manual_deletion"}
        )
    )

    return {
        "message": "Patient deleted successfully",
        "message_ar": "تم حذف المريض بنجاح"
    }


@router.post("/{patient_id}/consent")
async def update_patient_consent(
    patient_id: int,
    consent_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update patient consent for INPDP compliance
    تحديث موافقة المريض للامتثال لقوانين حماية البيانات
    """
    patient = db.query(Patient).filter(Patient.id == patient_id).first()

    if not patient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": "Patient not found",
                "message_ar": "المريض غير موجود"
            }
        )

    # Check permissions
    if not PermissionChecker.can_modify_patient(
        current_user.role, current_user.id, patient.dentist_id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "Insufficient permissions to modify patient consent",
                "message_ar": "صلاحيات غير كافية لتعديل موافقة المريض"
            }
        )

    # Update consent fields
    if "consent_given" in consent_data:
        patient.consent_given = consent_data["consent_given"]
        if consent_data["consent_given"]:
            patient.consent_date = datetime.utcnow()

    if "data_processing_consent" in consent_data:
        patient.data_processing_consent = consent_data["data_processing_consent"]

    if "research_consent" in consent_data:
        patient.research_consent = consent_data["research_consent"]

    patient.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(patient)

    # Log consent update
    await log_user_action(
        db=db,
        user_id=current_user.id,
        action=AuditActions.CONSENT_UPDATE,
        resource_type=AuditResourceTypes.PATIENT,
        resource_id=str(patient_id),
        details=create_audit_details(
            new_data=consent_data,
            additional_info={"consent_update_time": datetime.utcnow().isoformat()}
        )
    )

    return {
        "message": "Patient consent updated successfully",
        "message_ar": "تم تحديث موافقة المريض بنجاح",
        "consent_status": {
            "consent_given": patient.consent_given,
            "data_processing_consent": patient.data_processing_consent,
            "research_consent": patient.research_consent,
            "consent_date": patient.consent_date.isoformat() if patient.consent_date else None
        }
    }
