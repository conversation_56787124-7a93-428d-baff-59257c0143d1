"""
Main FastAPI application for AI Dental Assistant
مساعد طبيب الأسنان الذكي - التطبيق الرئيسي
"""

from fastapi import FastAPI, HTTPException, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn
import os
from pathlib import Path

from backend.app.config import settings
from backend.database.connection import engine, get_db
from backend.database.models import Base
from backend.app.routers import auth, patients, analysis, reports, telemedicine, enhanced_analysis
from backend.app.middleware.security import SecurityMiddleware
from backend.app.middleware.logging import LoggingMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting AI Dental Assistant...")
    print("🦷 مساعد طبيب الأسنان الذكي يبدأ...")

    # Create database tables
    Base.metadata.create_all(bind=engine)

    yield

    # Shutdown
    print("👋 Shutting down AI Dental Assistant...")


# Create FastAPI app with bilingual metadata
app = FastAPI(
    title="AI Dental Assistant / مساعد طبيب الأسنان الذكي",
    description="""
    Comprehensive AI-powered dental assistant application for the Tunisian market.

    تطبيق مساعد طبيب أسنان ذكي شامل للسوق التونسي.

    ## Features / الميزات

    * **AI X-ray Analysis** - تحليل الأشعة بالذكاء الاصطناعي
    * **Patient Management** - إدارة المرضى
    * **Telemedicine** - الطب عن بُعد
    * **INPDP Compliance** - الامتثال لقوانين حماية البيانات
    * **Bilingual Support** - دعم ثنائي اللغة
    """,
    version="1.0.0",
    contact={
        "name": "AI Dental Assistant Support",
        "email": "<EMAIL>",
        "url": "https://dental-ai.tn"
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    },
    lifespan=lifespan
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Custom middleware
app.add_middleware(SecurityMiddleware)
app.add_middleware(LoggingMiddleware)

# Static files for uploaded images and reports
app.mount("/static", StaticFiles(directory="static"), name="static")

# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication / المصادقة"])
app.include_router(patients.router, prefix="/api/v1/patients", tags=["Patients / المرضى"])
app.include_router(analysis.router, prefix="/api/v1/analysis", tags=["AI Analysis / التحليل الذكي"])
app.include_router(enhanced_analysis.router, tags=["Enhanced AI Analysis / التحليل الذكي المحسن"])
app.include_router(reports.router, prefix="/api/v1/reports", tags=["Reports / التقارير"])
app.include_router(telemedicine.router, prefix="/api/v1/telemedicine", tags=["Telemedicine / الطب عن بُعد"])


@app.get("/", tags=["Root"])
async def root():
    """Root endpoint with bilingual welcome message"""
    return {
        "message": "Welcome to AI Dental Assistant",
        "message_ar": "مرحباً بكم في مساعد طبيب الأسنان الذكي",
        "version": "1.0.0",
        "status": "active",
        "docs": "/docs",
        "features": [
            "AI X-ray Analysis",
            "Patient Management",
            "Telemedicine",
            "INPDP Compliance",
            "Bilingual Support"
        ]
    }


@app.get("/health", tags=["Health Check"])
async def health_check():
    """Health check endpoint for monitoring"""
    return {
        "status": "healthy",
        "service": "AI Dental Assistant",
        "version": "1.0.0",
        "timestamp": "2024-01-01T00:00:00Z"
    }


@app.get("/api/v1/info", tags=["System Info"])
async def system_info():
    """System information endpoint"""
    return {
        "application": "AI Dental Assistant",
        "application_ar": "مساعد طبيب الأسنان الذكي",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "database": "PostgreSQL",
        "ai_framework": "PyTorch",
        "compliance": ["INPDP", "GDPR"],
        "languages": ["Arabic", "French"],
        "supported_formats": ["DICOM", "JPG", "PNG"],
        "features": {
            "ai_analysis": True,
            "patient_management": True,
            "telemedicine": True,
            "offline_mode": True,
            "bilingual_interface": True
        }
    }


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler with bilingual messages"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "error_ar": get_arabic_error_message(exc.status_code),
            "status_code": exc.status_code,
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )


def get_arabic_error_message(status_code: int) -> str:
    """Get Arabic error messages for common HTTP status codes"""
    error_messages = {
        400: "طلب غير صحيح",
        401: "غير مصرح",
        403: "ممنوع",
        404: "غير موجود",
        422: "بيانات غير صالحة",
        500: "خطأ في الخادم"
    }
    return error_messages.get(status_code, "خطأ غير معروف")


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
