"""
Simple FastAPI application for AI Dental Assistant
مساعد طبيب الأسنان الذكي - التطبيق المبسط
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse, HTMLResponse
from pydantic import BaseModel
import uvicorn
import os
import shutil
from pathlib import Path
from typing import Optional, List
import json
from datetime import datetime

# Create FastAPI app
app = FastAPI(
    title="AI Dental Assistant / مساعد طبيب الأسنان الذكي",
    description="""
    Comprehensive AI-powered dental assistant application.
    تطبيق مساعد طبيب أسنان ذكي شامل.
    
    ## Features / الميزات
    * **AI X-ray Analysis** - تحليل الأشعة بالذكاء الاصطناعي
    * **Patient Management** - إدارة المرضى
    * **Bilingual Support** - دعم ثنائي اللغة
    """,
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories if they don't exist
os.makedirs("uploads", exist_ok=True)
os.makedirs("static", exist_ok=True)
os.makedirs("temp", exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Pydantic models
class AnalysisResult(BaseModel):
    image_id: str
    predictions: List[dict]
    confidence: float
    recommendations: List[str]
    timestamp: str

class PatientInfo(BaseModel):
    name: str
    age: int
    gender: str
    phone: Optional[str] = None
    email: Optional[str] = None

# In-memory storage (replace with database in production)
patients_db = []
analysis_results = []

@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with HTML interface"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI Dental Assistant / مساعد طبيب الأسنان الذكي</title>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .feature { background: #ecf0f1; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .upload-area { border: 2px dashed #3498db; padding: 30px; text-align: center; margin: 20px 0; border-radius: 10px; }
            button { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #2980b9; }
            .arabic { direction: rtl; text-align: right; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🦷 AI Dental Assistant</h1>
            <h1 class="arabic">مساعد طبيب الأسنان الذكي</h1>
            
            <div class="feature">
                <h3>🔬 AI X-ray Analysis / تحليل الأشعة بالذكاء الاصطناعي</h3>
                <p>Upload dental X-ray images for AI-powered analysis</p>
                <p class="arabic">ارفع صور الأشعة السينية للأسنان للتحليل بالذكاء الاصطناعي</p>
            </div>
            
            <div class="upload-area">
                <h3>📤 Upload X-ray Image</h3>
                <form action="/api/v1/analysis/upload" method="post" enctype="multipart/form-data">
                    <input type="file" name="file" accept="image/*,.dcm" required>
                    <br><br>
                    <button type="submit">Analyze Image / تحليل الصورة</button>
                </form>
            </div>
            
            <div class="feature">
                <h3>📊 API Endpoints</h3>
                <ul>
                    <li><a href="/docs">📚 Interactive API Documentation</a></li>
                    <li><a href="/health">💚 Health Check</a></li>
                    <li><a href="/api/v1/info">ℹ️ System Information</a></li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "AI Dental Assistant",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/info")
async def system_info():
    """System information endpoint"""
    return {
        "application": "AI Dental Assistant",
        "application_ar": "مساعد طبيب الأسنان الذكي",
        "version": "1.0.0",
        "ai_framework": "PyTorch",
        "languages": ["Arabic", "French", "English"],
        "supported_formats": ["DICOM", "JPG", "PNG"],
        "features": {
            "ai_analysis": True,
            "patient_management": True,
            "bilingual_interface": True,
            "file_upload": True
        },
        "endpoints": {
            "upload": "/api/v1/analysis/upload",
            "patients": "/api/v1/patients",
            "health": "/health",
            "docs": "/docs"
        }
    }

@app.post("/api/v1/analysis/upload")
async def upload_and_analyze(file: UploadFile = File(...)):
    """Upload and analyze dental X-ray image"""
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Save uploaded file
        file_path = f"uploads/{file.filename}"
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Simulate AI analysis (replace with actual AI model)
        analysis_result = {
            "image_id": f"img_{len(analysis_results) + 1}",
            "filename": file.filename,
            "file_path": file_path,
            "predictions": [
                {
                    "class": "normal",
                    "name": "أسنان طبيعية",
                    "confidence": 0.85,
                    "severity": "low"
                },
                {
                    "class": "caries",
                    "name": "تسوس",
                    "confidence": 0.12,
                    "severity": "medium"
                }
            ],
            "overall_confidence": 0.85,
            "recommendations": [
                "Regular dental checkup recommended",
                "فحص دوري للأسنان مُوصى به"
            ],
            "timestamp": datetime.now().isoformat(),
            "processing_time": "2.3 seconds"
        }
        
        # Store result
        analysis_results.append(analysis_result)
        
        return {
            "success": True,
            "message": "Image analyzed successfully",
            "message_ar": "تم تحليل الصورة بنجاح",
            "result": analysis_result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.get("/api/v1/analysis/results")
async def get_analysis_results():
    """Get all analysis results"""
    return {
        "total_analyses": len(analysis_results),
        "results": analysis_results
    }

@app.post("/api/v1/patients")
async def create_patient(patient: PatientInfo):
    """Create a new patient record"""
    patient_data = patient.dict()
    patient_data["id"] = len(patients_db) + 1
    patient_data["created_at"] = datetime.now().isoformat()
    
    patients_db.append(patient_data)
    
    return {
        "success": True,
        "message": "Patient created successfully",
        "message_ar": "تم إنشاء سجل المريض بنجاح",
        "patient": patient_data
    }

@app.get("/api/v1/patients")
async def get_patients():
    """Get all patients"""
    return {
        "total_patients": len(patients_db),
        "patients": patients_db
    }

@app.get("/api/v1/patients/{patient_id}")
async def get_patient(patient_id: int):
    """Get specific patient by ID"""
    for patient in patients_db:
        if patient["id"] == patient_id:
            return patient
    
    raise HTTPException(status_code=404, detail="Patient not found")

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler with bilingual messages"""
    error_messages_ar = {
        400: "طلب غير صحيح",
        401: "غير مصرح",
        403: "ممنوع",
        404: "غير موجود",
        422: "بيانات غير صالحة",
        500: "خطأ في الخادم"
    }
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "error_ar": error_messages_ar.get(exc.status_code, "خطأ غير معروف"),
            "status_code": exc.status_code,
            "timestamp": datetime.now().isoformat()
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
